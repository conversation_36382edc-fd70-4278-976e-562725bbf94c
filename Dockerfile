# Use the official Node.js image as the base image
FROM node:20-alpine

# Set the working directory inside the container
WORKDIR /usr/src/app/customer

# Copy only the package.json and yarn.lock first to leverage Docker layer caching
COPY package.json yarn.lock ./

# Install dependencies using Yarn
RUN yarn install

# Copy the rest of the application code
COPY . .

# Build the Next.js application
RUN yarn build

# Expose the application port (Next.js default is 3000, change if needed)
EXPOSE 4000

# Start the Next.js application using the start script
CMD ["yarn", "start"]
