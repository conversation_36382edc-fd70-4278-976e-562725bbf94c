{"name": "freshtocart-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 4000", "lint": "next lint", "prettier:write": "prettier --write .", "prettier:check": "prettier --check ."}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@react-google-maps/api": "^2.19.3", "@types/node": "20.5.6", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "eslint-config-next": "^14.0.4", "lucide-react": "^0.292.0", "next": "^14.0.4", "next-auth": "^4.24.5", "postcss": "8.4.28", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.2", "react-icons": "^5.5.0", "react-modal": "^3.16.3", "react-modal-video": "^2.0.1", "react-slick": "^0.29.0", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.0.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "zustand": "^4.4.7"}, "devDependencies": {"@types/react-modal": "^3.16.3", "@types/react-modal-video": "^1.2.3", "@types/react-slick": "^0.23.10", "prettier": "^3.5.3"}}