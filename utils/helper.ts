export const getCartItem = ({
  productId,
  variantId,
  marinationId,
  cartlistState,
  dataId,
}: {
  productId?: string;
  variantId?: string;
  marinationId?: string;
  cartlistState: any;
  dataId: string;
}) => {
  const targetProductId = productId || dataId;

  return cartlistState.carts?.find((item: any) => {
    // Check if product ID matches
    const productMatches = item.productId === targetProductId;

    if (!productMatches) return false;

    // If no variants or marination specified, match cart item without variant/marination
    if (!variantId && !marinationId) {
      return !item.variant && !item.marinationType;
    }

    // If looking for specific variant, check if it matches
    if (variantId && !marinationId) {
      return item.variant === variantId;
    }

    // If looking for specific marination, check if it matches
    if (marinationId && !variantId) {
      return item.marinationType === marinationId;
    }

    // If looking for both variant and marination, both must match
    if (variantId && marinationId) {
      return item.variant === variantId && item.marinationType === marinationId;
    }

    return false;
  });
};

export const toFloat = (number: any) =>
  (Math.round(number * 100) / 100).toFixed(2);

export const hasVariantsOrMarination = (data: any) => {
  return (
    (data?.variants &&
      data.variants.filter((item: any) => item.status === "Active").length >
        0) ||
    (data?.marinationTypes && data.marinationTypes.length > 0)
  );
};

/**
 * Calculate the total price for a cart item including variant and marination values
 * @param basePrice - The base product price (offerPrice or mrp)
 * @param cartItem - The cart item with variant and marination details
 * @param quantity - The quantity of the item
 * @returns The total price including variant and marination values
 */
export const calculateCartItemPrice = (
  basePrice: number,
  cartItem: any,
  quantity: number = 1
): number => {
  let totalPrice = basePrice;

  // Add variant value if present
  if (cartItem?.variant?.value) {
    totalPrice += cartItem.variant.value;
  }

  // Add marination value if present
  if (cartItem?.marinationType?.value) {
    totalPrice += cartItem.marinationType.value;
  }

  return totalPrice * quantity;
};

/**
 * Calculate the total for all cart items including variant and marination values
 * @param cartData - Array of cart items from API
 * @returns The total amount including all variant and marination values
 */
export const calculateCartTotal = (cartData: any[]): number => {
  let total = 0;

  cartData?.forEach((item: any) => {
    const basePrice = item?.offerPrice ?? item?.mrp ?? 0;

    item?.cartDetails?.forEach((cartItem: any) => {
      total += calculateCartItemPrice(basePrice, cartItem, cartItem.quantity);
    });
  });

  return total;
};

export const convertToGrams = (weightStr: string, multiplier = 1): number => {
  const valueMatch = weightStr.match(/\d+(\.\d+)?/);
  const value = valueMatch ? parseFloat(valueMatch[0]) : 0;

  const unitMatch = weightStr.toLowerCase().includes("kg") ? "kg" : "g";

  if (unitMatch === "kg") {
    return value * 1000 * multiplier;
  } else {
    return value * multiplier;
  }
};

export const getCurrentSlot = (data: any[]) => {
  if (!data?.length) return "";

  const fromStr = data.find((item: any) => item.key === "from")?.name;
  const toStr = data.find((item: any) => item.key === "to")?.name;

  const parseTime = (str: string) => {
    const match = str.match(/(\d+)(AM|PM)/);
    if (!match) return 0;
    let [_, hourStr, meridian] = match;
    let hour = parseInt(hourStr, 10);
    if (meridian === "PM" && hour !== 12) hour += 12;
    if (meridian === "AM" && hour === 12) hour = 0;
    return hour;
  };

  const fromHour = parseTime(fromStr);
  const toHour = parseTime(toStr);

  const now = new Date();
  const nowMinute = now.getMinutes();

  const slotStart = new Date(now);
  if (nowMinute < 30) {
    slotStart.setMinutes(30, 0, 0);
  } else {
    slotStart.setHours(slotStart.getHours() + 1, 0, 0, 0);
  }

  if (slotStart.getHours() >= toHour) {
    slotStart.setDate(slotStart.getDate() + 1);
    slotStart.setHours(fromHour, 0, 0, 0);
  }

  const slotEnd = new Date(slotStart.getTime() + 2 * 60 * 60 * 1000);

  console.log(slotStart, slotEnd);

  const format = (date: Date) => {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const displayHour = hours % 12 || 12;
    const period = hours >= 12 ? "PM" : "AM";
    const displayMinutes = minutes === 0 ? "" : ":30";
    return `${displayHour}${displayMinutes}${period}`;
  };

  return `${format(slotStart)} - ${format(slotEnd)}`;
};
