interface Params {
  method?: string;
  url: string;
  body?: object;
  headers?: HeadersInit;
  token?: string;
}

const submitData = async ({
  url,
  body,
  headers,
  token,
  method,
}: Params): Promise<any> => {
  try {
    const header: HeadersInit = new Headers();
    header.append("Content-Type", "application/json");
    if (token) header.append("Authorization", `Bearer ${token}`);

    const requestConfig: RequestInit = {
      method: method || "POST",
      headers: headers || header,
      body: JSON.stringify(body || {}),
    };
    const response = await fetch(url, requestConfig);
    const result = await response.json();
    return result;
  } catch (error) {
    return error;
  }
};

export default submitData;
