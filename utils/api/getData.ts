interface Props {
  url: string;
  method?: string;
  cache?: RequestCache;
  body?: object;
}

const getData = async ({ url, method, cache, body }: Props) => {
  try {
    const res = await fetch(url, {
      method: method || "post",
      cache: cache || "no-cache",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    const result = await res.json();
    if (!res.ok || !result?.status) {
      // This will activate the closest `error.js` Error Boundary
      throw new Error("Failed to fetch data");
    }
    const { data } = result;
    return data;
  } catch (error) {
    return null;
  }
};

export default getData;
