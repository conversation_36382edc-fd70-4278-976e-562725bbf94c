const url = process.env.BACKEND_URL;

const server = {
  getCategories: `${url}/api/catgories`,
  getBanners: `${url}/api/banners`,
  getProductsByCategory: `${url}/api/products/category`,
  getFlashProduct: `${url}/api/flash`,
  getNewlyArrived: `${url}/api/newlyarrived`,
  getTrendingProduct: `${url}/api/trending`,
  getCompoProduct: `${url}/api/compo/list`,
  dealOfTheDay: `${url}/api/dealoftheday`,
  getBlogs: `${url}/api/blogs`,
  getBlog: `${url}/api/blog`,
  getNews: `${url}/api/news`,
  getLatestNews: `${url}/api/news/latest`,
  getMostReadNews: `${url}/api/news/most`,
  getCms: `${url}/api/cms`,
};

const client = {
  getCategories: `/backend/catgories`,
  getNewlyArrived: `/backend/newlyarrived`,
  getTrendingProduct: `/backend/trending`,
  getCompoProduct: `/backend/compo/list`,
  getFlashProduct: `/backend/flash`,
  dealOfTheDay: `/backend/dealoftheday`,
  submitEnquiry: `/backend/enquiry/submit`,
  getBlogs: `/backend/blogs`,
  getProductsByCategory: `/backend/products/category`,
  chooseLocation: `/backend/delivery/chooseLocation`,

  //auth
  requestOtp: "/backend/auth/login",
  verifyOtp: "/backend/auth/verify",
  //cart
  getCartList: "/backend/carts",
  cartItems: "/backend/cartitems",
  increaseCart: "/backend/cart/increase",
  decreaseCart: "/backend/cart/decrease",
  removeCart: "/backend/cart/remove",
  addToCart: "/backend/cart/addtocart",

  //checkout
  checkout: "/backend/checkout",

  //product
  getProduct: "/backend/product",

  //order
  placeOrder: "/backend/order/orderplace",
  myOrder: "/backend/orders",
  order: "/backend/order",
  cancelOrder: "/backend/order/cancel",

  //wishlist
  addWishList: "/backend/wishlist/addAndRemove",
  wishLists: "/backend/wishlists",
  wishListItems: "/backend/wishlist/items",

  //cms
  getCms: "/backend/cms",
  getFaq: "/backend/faq",
  getTermsAndCondition: "/backend/termsAndCondition",
  getCancellationPolicy: "/backend/cancellationPolicy",
  getShippingPolicy: "/backend/shippingPolicy",
  getPrivacyPolicy: "/backend/privacyPolicy",
  getRefundPolicy: "/backend/refundPolicy",
  getReturnPolicy: "/backend/returnPolicy",
  getAboutUs: "/backend/aboutUs",

  //user
  addAddress: "/backend/user/addAddress",
  getAllAddress: "/backend/user/address",
  getAddress: "/backend/user/getAddress",
  getMainAddress: "/backend/user/getActiveAddress",
  updateAddress: "/backend/user/address/update",
  removeAddress: "/backend/user/address/remove",
  geActiveAddress: "/backend/user/getActiveAddress",
  chooseAddress: "/backend/user/address/choose",

  getslots: "/backend/user-timeslots",

  getUserInfo: "/backend/user/info",
  updateProfile: "/backend/user/update",
  updateAvatar: "/backend/user/update/image",

  //gmap
  placeSearch: "/backend/place/auto",
  placeAddress: "/backend/place/address",

  //promocode
  promoCodeList: "/backend/promocode/list",
  promoCodeApply: "/backend/promocode/apply",

  //highlite
  getHighlite: "/backend/highlite",
  // deliveryTime
  getDeliveryTime: "/backend/deliveryTime",
};

const payment = {
  initializePayment: `/payment/initiate`,
  verifyPayment: `/backend/payments/verify`,
  retryPayment: `/backend/payments/retry`,
  hdfcVerify: `/backend/payment/hdfc/verify`,
};

export default { server, client, payment };
