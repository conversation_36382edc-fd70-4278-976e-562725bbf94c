// Example usage of the logout functionality
// This file is for reference only - you can delete it after understanding the usage

import { useRouter } from "next/navigation";
import { handleLogout, handleLogoutWithoutRouter } from "./navigation";

// Example 1: Using logout with router (recommended)
export const LogoutButtonWithRouter = () => {
  const router = useRouter();

  const handleClick = () => {
    handleLogout(router);
  };

  return (
    <button
      onClick={handleClick}
      className="bg-red-500 text-white px-4 py-2 rounded"
    >
      Logout
    </button>
  );
};

// Example 2: Using logout without router (fallback)
export const LogoutButtonWithoutRouter = () => {
  const handleClick = () => {
    handleLogoutWithoutRouter();
  };

  return (
    <button
      onClick={handleClick}
      className="bg-red-500 text-white px-4 py-2 rounded"
    >
      Logout (No Router)
    </button>
  );
};

// Example 3: Using in a component with confirmation
export const LogoutButtonWithConfirmation = () => {
  const router = useRouter();

  const handleClick = () => {
    if (confirm("Are you sure you want to logout?")) {
      handleLogout(router);
    }
  };

  return (
    <button
      onClick={handleClick}
      className="bg-red-500 text-white px-4 py-2 rounded"
    >
      Logout (With Confirmation)
    </button>
  );
};

// Example 4: Using in an async function
export const useLogoutHandler = () => {
  const router = useRouter();

  const logout = async () => {
    try {
      // You can add additional cleanup here before logout
      console.log("Performing logout...");

      await handleLogout(router);

      // Any additional cleanup after logout
      console.log("Logout completed");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return { logout };
};
