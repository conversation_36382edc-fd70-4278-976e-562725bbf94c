"use client";

import { useState, useEffect } from "react";

interface Props<T> {
  url: string;
  options?: RequestInit;
  initialData?: T;
  dep?: any;
  start?: any;
  token?: string;
}

export const useFetch = <T>({
  url,
  options,
  initialData,
  dep,
  start,
  token,
}: Props<T>) => {
  const [data, setData] = useState<T | null>(initialData ?? null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<any>(null);
  const [isCompleted, setisCompleted] = useState(false);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const headers = new Headers();
        headers.append("Content-Type", "application/json");
        token && headers.append("Authorization", `Bearer ${token}`);
        const tempOptions: RequestInit = {
          method: options?.method || "post",
          headers: options?.headers || headers,
          body: options?.body || JSON.stringify({}),
        };
        const response = await fetch(url, tempOptions);
        if (!response.ok) throw new Error(response.statusText);
        const json = await response.json();
        const { data } = json;
        setData(data);
      } catch (error) {
        setError(`${error} Could not Fetch Data `);
      } finally {
        setIsLoading(false);
        setisCompleted(true);
      }
    };
    start && fetchData();
  }, [url, dep, start, token]);
  return { data, isLoading, error, isCompleted };
};
