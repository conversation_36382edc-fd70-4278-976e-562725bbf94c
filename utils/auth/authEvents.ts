/**
 * Authentication event utilities for managing auth state changes across the application
 */

/**
 * Dispatches a custom event to notify all components that authentication state has changed
 * This should be called whenever:
 * - User logs in
 * - User logs out
 * - Token is updated
 */
export const dispatchAuthStateChange = () => {
  if (typeof window !== "undefined") {
    window.dispatchEvent(new Event("authStateChanged"));
  }
};

/**
 * Sets up event listeners for authentication state changes
 * @param callback Function to call when auth state changes
 * @returns Cleanup function to remove event listeners
 */
export const setupAuthStateListener = (callback: () => void) => {
  if (typeof window === "undefined") {
    return () => {}; // Return empty cleanup function for SSR
  }

  // Listen for storage changes (when localStorage is updated in other tabs/windows)
  const handleStorageChange = (e: StorageEvent) => {
    if (e.key === "accessToken") {
      callback();
    }
  };

  // Listen for custom auth events (for same-tab updates)
  const handleAuthChange = () => {
    callback();
  };

  window.addEventListener("storage", handleStorageChange);
  window.addEventListener("authStateChanged", handleAuthChange);

  // Return cleanup function
  return () => {
    window.removeEventListener("storage", handleStorageChange);
    window.removeEventListener("authStateChanged", handleAuthChange);
  };
};

/**
 * Gets the current authentication token from localStorage
 * @returns The access token or undefined if not found
 */
export const getCurrentToken = (): string | undefined => {
  if (typeof window === "undefined") {
    return undefined; // Return undefined for SSR
  }
  return localStorage.getItem("accessToken") ?? undefined;
};

/**
 * Sets the authentication token and dispatches auth state change event
 * @param token The access token to set
 */
export const setAuthToken = (token: string) => {
  if (typeof window !== "undefined") {
    localStorage.setItem("accessToken", token);
    dispatchAuthStateChange();
  }
};

/**
 * Removes the authentication token and dispatches auth state change event
 */
export const removeAuthToken = () => {
  if (typeof window !== "undefined") {
    localStorage.removeItem("accessToken");
    dispatchAuthStateChange();
  }
};
