import { Backend_URL } from "@/utils/constant";
import { NextAuthOptions } from "next-auth";
import { JWT } from "next-auth/jwt";
import NextAuth from "next-auth/next";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: {
          label: "Username",
          type: "text",
          placeholder: "jsmith",
        },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials: any, req: any) {
        if (!credentials?.username || !credentials?.password) return null;
        const { username, password } = credentials;
        console.log("ffdfd", Backend_URL);
        const res = await fetch(Backend_URL + "/api/auth/verify", {
          method: "POST",
          body: JSON.stringify({
            phone: username,
            otp: password,
          }),
          headers: {
            "Content-Type": "application/json",
          },
        });
        if (res.status == 401) {
          console.log(res?.statusText, "dddd");
          return null;
        }
        const result = await res.json();
        if (result?.status) {
          const user = result?.data;
          return user;
        }
        return null;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }: any) {
      if (user) return { ...token, ...user };
      return token;
      if (new Date().getTime() < token.expiresIn) return token;

      // return await refreshToken(token);
    },

    async session({ token, session }: any) {
      session.user = token.user;
      session.token = token.token;
      return session;
    },
  },
  pages: {
    signIn: "/login",
  },
};
