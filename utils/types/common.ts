import { Product } from "./product";

export interface Banner {
  title: string;
  desctiption: string;
  image: string;
  id?: string;
}

export interface Blog {
  title: string;
  image: string;
  desctiption?: string;
  shortDescription: string;
  authorName?: string;
  authorImage?: string;
  id?: string;
  createdAt: string;
}

export interface Cart {
  productId: Product;
  quantity: number;
  id: string;
}

export interface Address {
  location?: ILocation;
  id: string;
  name: string;
  address?: string;
  address2: string;
  landmark: string;
  city?: string;
  pincode?: string;
  phone: string;
}

interface ILocation {
  lat: string | number;
  lng: string | number;
}

export interface Order {
  invoice: Invoice;
  shippingAddress: Address;
  user: string;
  orderStatus: number;
  items: OrderItem[];
  paymentMethod: string;
  totalAmount: number;
  createdAt: Date;
  id: string;
  statusHistory: STATUS_HISTORY;
}

export interface STATUS_HISTORY {
  processed_on?: Date;
  ordered_on?: Date;
  awaitPickup_on?: Date;
  shipped_on?: Date;
  delivered_on?: Date;
  cancelled_on?: Date;
  rejected_on?: Date;
  userCancelled_on?: Date;
}

interface Invoice {
  url: string;
}

export interface OrderItem {
  product: Product;
  price: number;
  quantity: number;
  amount: number;
  image: string;
  title: string;
  id: string;
}

export interface IWishlist {
  user: string;
  productId: Product;
  id: string;
}

export interface Icms {
  title: string;
  content: string;
}

export interface Ifaq {
  question: string;
  ans: string;
  tag: string;
}
