export interface MarinationType {
  _id: string;
  title: string;
  ingredients: string;
  status: string;
  image: string;
  rating: string;
  isActive: boolean;
  isDeleted: boolean;
  addedBy: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Variant {
  _id: string;
  status: string;
  id: {
    _id: string;
    title: string;
    description?: string;
    image: string;
  };
}

export interface Product {
  _id: string;
  title: string;
  productType?: number;
  price: number;
  image?: string;
  images?: string[];
  unit?: string;
  hint?: string;
  mrp?: number;
  // compo: Compo[];
  description?: string;
  rating?: string;
  stock?: any;
  isActive?: boolean;
  child?: any[];
  category?: any;
  storageInstruction?: string;
  id?: string;
  isVeg: boolean;
  weight: string;
  offerPrice?: number;
  discount?: number;
  averageRating?: any;
  totalReviews?: any;
  defaultMasala?: any;
  marinationTypes?: MarinationType[];
  variants?: Variant[];
}

export interface AddToCartParams {
  productId: string;
  variantId?: string;
  marinationId?: string;
}
