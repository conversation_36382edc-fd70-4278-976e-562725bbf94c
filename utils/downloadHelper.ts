/**
 * Utility functions for handling file downloads
 */

/**
 * Downloads a file from a given URL
 * @param url - The URL of the file to download
 * @param filename - Optional custom filename for the download
 */
export const downloadFile = (url: string, filename?: string) => {
  try {
    // Create a temporary anchor element
    const link = document.createElement("a");
    link.href = url;

    // Set the download attribute with filename if provided
    if (filename) {
      link.download = filename;
    } else {
      // Extract filename from URL or use a default
      const urlParts = url.split("/");
      const urlFilename = urlParts[urlParts.length - 1];
      link.download = urlFilename || "download";
    }

    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Error downloading file:", error);
    // Fallback: open in new tab
    window.open(url, "_blank");
  }
};

/**
 * Downloads an invoice file
 * @param invoiceUrl - The URL of the invoice
 * @param orderId - The order ID to include in filename
 */
export const downloadInvoice = (invoiceUrl: string, orderId: string) => {
  const filename = `invoice-${orderId}.pdf`;
  downloadFile(invoiceUrl, filename);
};

/**
 * Handles click event for download buttons to prevent event propagation
 * @param event - The click event
 * @param downloadFn - Function to execute for download
 */
export const handleDownloadClick = (
  event: React.MouseEvent,
  downloadFn: () => void,
) => {
  event.stopPropagation(); // Prevent parent click events
  event.preventDefault(); // Prevent default link behavior
  downloadFn();
};
