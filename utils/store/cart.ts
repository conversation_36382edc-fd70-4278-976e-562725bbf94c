import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

interface CartListState {
  carts: any[];
  setCartList: (data: any) => void;
  getCartCount: () => number;
  clearCart: () => void;
}

export const useCartStore = create<CartListState>()(
  persist(
    (set, get) => ({
      carts: [],
      setCartList: (data) => {
        set(() => ({ carts: data }));
      },
      getCartCount: () => {
        return get()?.carts?.length;
      },
      clearCart: () => {
        set(() => ({ carts: [] }));
      },
    }),
    {
      name: "cart",
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
);
