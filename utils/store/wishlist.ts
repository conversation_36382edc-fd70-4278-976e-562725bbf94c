import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

interface WishListState {
  wishList: any[];
  setWishList: (data: any[]) => void;
  isInWishlist: (productId: string) => boolean;
  getWishlistCount: () => number;
  clearWishlist: () => void;
}

export const useWishlistStore = create<WishListState>()(
  persist(
    (set, get) => ({
      wishList: [],
      setWishList: (data) => {
        set({ wishList: data });
      },
      isInWishlist: (productId) => {
        const currentWishlist = get().wishList;
        return currentWishlist.some((item) => item.productId === productId);
      },
      getWishlistCount: () => {
        return get()?.wishList?.length;
      },
      clearWishlist: () => {
        set({ wishList: [] });
      },
    }),
    {
      name: "wishlist",
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
);
