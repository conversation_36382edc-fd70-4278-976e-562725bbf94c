import { signOut } from "next-auth/react";
import { useCartStore } from "./store/cart";
import { useWishlistStore } from "./store/wishlist";

/**
 * Navigation utility functions for redirecting to specific profile tabs
 */

export const navigateToProfileTab = (router: any, tab: string) => {
  router.push(`/profile?tab=${tab}`);
};

export const navigateToMyOrders = (router: any) => {
  router.push("/profile?tab=orders");
};

export const navigateToProfile = (router: any) => {
  router.push("/profile?tab=profile");
};

export const navigateToAddress = (router: any) => {
  router.push("/profile?tab=address");
};

export const navigateToWishlist = (router: any) => {
  router.push("/profile?tab=wishlist");
};

export const navigateToWallet = (router: any) => {
  router.push("/profile?tab=wallet");
};

/**
 * Logout utility function that clears localStorage, stores, and redirects properly
 * This ensures logout works correctly regardless of the port the app is running on
 */
export const handleLogout = async (router: any) => {
  try {
    // Clear all authentication-related localStorage items
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("user");
    localStorage.removeItem("token");

    // Clear Zustand stores using getState() to access store methods
    useCartStore.getState().clearCart();
    useWishlistStore.getState().clearWishlist();

    // Clear sessionStorage items (where Zustand stores persist)
    sessionStorage.removeItem("cart");
    sessionStorage.removeItem("wishlist");

    // Sign out from NextAuth without automatic redirect to avoid port issues
    await signOut({ redirect: false });

    // Redirect to home page on current port/domain
    router.push("/");

    console.log(
      "Logout successful - cleared localStorage, sessionStorage, and stores",
    );
  } catch (error) {
    console.error("Logout error:", error);
    // Fallback: still redirect to home even if signOut fails
    router.push("/");
  }
};

/**
 * Alternative logout function that can be used without router (for components that don't have access to router)
 */
export const handleLogoutWithoutRouter = async () => {
  try {
    // Clear all authentication-related localStorage items
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("user");
    localStorage.removeItem("token");

    // Clear Zustand stores
    useCartStore.getState().clearCart();
    useWishlistStore.getState().clearWishlist();

    // Clear sessionStorage items
    sessionStorage.removeItem("cart");
    sessionStorage.removeItem("wishlist");

    // Sign out from NextAuth without automatic redirect
    await signOut({ redirect: false });

    // Use window.location for redirect (fallback)
    window.location.href = "/";

    console.log(
      "Logout successful - cleared localStorage, sessionStorage, and stores",
    );
  } catch (error) {
    console.error("Logout error:", error);
    // Fallback redirect
    window.location.href = "/";
  }
};
