import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: "3rem",
        xs: "0.5rem",
        sm: "1rem",
        md: "4rem",
        lg: "4rem",
      },
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        red: {
          50: "#D33939",
          60: "#DD3A3A",
        },
        light: {
          50: "#F9F9F9",
          100: "#e5e5e5",
        },
        primary: {
          100: "#3559A8",
          200: "#3F8DCC",
        },
        dark: {
          10: "#2B2B2B",
          50: "#1C1C1C",
          100: "#686868",
          150: "#606060",
          200: "#939393",
          250: "#2d2d2d",
          300: "#383838",
          350: "#353535",
          400: "#232323",
          450: "#8C8C8C",
          500: "#3D3D3D",
        },
      },
      fontFamily: {
        inter: ["var(--font-inter)"],
        poppins: ["var(--font-roboto-mono)"],
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
export default config;
