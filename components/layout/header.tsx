import React from "react";
import Image from "next/image";
import apiUrl from "@/utils/api/routes";
import Link from "next/link";
import Search from "@/components/main/search";
import CategorySection from "../main/category";
import Auth from "./auth";
import Location from "./location";
import dynamic from "next/dynamic";
const CartMenu = dynamic(() => import("./cart"), {
  loading: () => <p>Loading...</p>,
});

async function getData() {
  try {
    const res = await fetch(apiUrl.server.getCategories, {
      method: "post",
      cache: "no-cache",
    });
    const result = await res.json();
    if (!res.ok || !result?.status) {
      throw new Error("Failed to fetch data");
    }
    const { data } = result;
    return data;
  } catch (error) {
    console.log(error);
    return null;
  }
}

async function Header() {
  const { data = [] }: { data: any } = await getData();

  return (
    <div className="h-20 w-full bg-white py-2 drop-shadow-lg sticky top-0 z-40">
      <div className="container flex justify-between">
        <div className="flex justify-start items-center gap-5">
          <Link href="/">
            <Image
              src="/images/logo.png"
              alt="logo"
              width={83}
              height={50}
              className="object-contain"
              style={{ height: "50px" }}
            />
          </Link>
          <div>
            <Location />
          </div>

          <Search />

          <CategorySection data={data} />
        </div>
        <div className="flex justify-between items-center gap-10">
          <Link href="">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="27"
              height="28"
              viewBox="0 0 27 28"
              fill="none"
            >
              <path
                d="M26.475 18.9084L23.475 7.90839C22.875 5.60839 21.375 3.50839 19.475 2.10839C17.575 0.708385 15.175 -0.0916145 12.775 0.0083855C10.275 0.108386 7.975 0.908385 6.075 2.50839C4.275 4.00839 2.975 6.10839 2.375 8.50839L0.075 19.1084C-0.025 19.6084 -0.025 20.1084 0.075 20.6084C0.175 21.1084 0.375 21.6084 0.775 22.0084C1.075 22.4084 1.475 22.7084 1.975 22.9084C2.475 23.1084 2.975 23.2084 3.475 23.2084H7.775V23.3084C8.075 24.6084 8.775 25.8084 9.775 26.6084C10.775 27.4084 12.075 27.9084 13.375 27.9084C14.675 27.9084 15.975 27.4084 16.975 26.6084C17.975 25.8084 18.675 24.6084 18.975 23.3084V23.2084H23.175C23.675 23.2084 24.175 23.1084 24.675 22.8084C25.175 22.6084 25.575 22.2084 25.875 21.8084C26.175 21.4084 26.375 20.9084 26.475 20.4084C26.575 20.0084 26.575 19.4084 26.475 18.9084ZM15.475 25.1084C14.875 25.5084 14.175 25.8084 13.375 25.8084C12.575 25.8084 11.875 25.6084 11.275 25.1084C10.675 24.7084 10.175 24.1084 9.975 23.3084L9.875 23.1084H16.875L16.775 23.3084C16.475 24.1084 16.075 24.7084 15.475 25.1084ZM23.775 21.0084C23.575 21.1084 23.375 21.1084 23.175 21.1084H3.375C3.175 21.1084 2.975 21.1084 2.775 21.0084C2.575 20.9084 2.475 20.8084 2.275 20.6084C2.175 20.4084 2.075 20.3084 1.975 20.1084C1.975 19.9084 1.975 19.7084 1.975 19.5084L4.475 8.90839C4.875 7.00839 5.975 5.30839 7.475 4.10839C8.975 2.90839 10.875 2.20839 12.775 2.10839C12.875 2.10839 12.975 2.10839 13.075 2.10839C14.875 2.10839 16.775 2.70839 18.275 3.80839C19.875 4.90839 20.975 6.60839 21.475 8.50839L24.475 19.5084C24.575 19.7084 24.575 19.9084 24.475 20.1084C24.475 20.3084 24.375 20.5084 24.275 20.6084C24.175 20.7084 23.875 20.9084 23.775 21.0084Z"
                fill="#3D3D3D"
              />
            </svg>
          </Link>

          <Link href="/wishlist">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="31"
              height="29"
              viewBox="0 0 31 29"
              fill="none"
            >
              <path
                d="M15.1596 28.1557C14.5025 28.0698 13.8823 27.8034 13.3675 27.3862C13.0618 27.1859 12.6823 26.9329 12.1763 26.6377C8.60472 24.5976 5.49024 21.8455 3.02601 18.5522C1.05816 16.0462 -0.00103432 12.9466 0.0216458 9.76044C-0.0163042 8.21465 0.342697 6.68495 1.0643 5.31741C1.78591 3.94987 2.84605 2.79016 4.1435 1.94902C5.48216 1.14639 7.00021 0.691224 8.55965 0.624944C10.1191 0.558663 11.6704 0.883367 13.0723 1.56951C13.7226 1.88128 14.3529 2.23335 14.9593 2.62369L15.1596 2.75019C16.4045 1.7999 17.8446 1.13745 19.3762 0.810513C20.5906 0.533042 21.8488 0.505845 23.074 0.730584C24.2991 0.955323 25.4657 1.42729 26.5025 2.11768C27.4896 2.80144 28.3237 3.68315 28.9517 4.70673C29.5796 5.73032 29.9876 6.87338 30.1499 8.06322C30.4212 9.90189 30.3104 11.7768 29.8243 13.5706C29.3382 15.3645 28.4872 17.0388 27.3247 18.489C24.8693 21.7931 21.7531 24.5502 18.1745 26.585C17.6685 26.8907 17.2889 27.1437 16.9727 27.344C16.4587 27.783 15.8293 28.0648 15.1596 28.1557ZM8.83451 2.72911C7.5572 2.73213 6.30706 3.09769 5.22933 3.78328C4.22647 4.43745 3.40872 5.33865 2.85486 6.40021C2.30101 7.46177 2.02966 8.64799 2.06681 9.84477C2.08266 12.5679 3.01876 15.2056 4.72322 17.3294C7.02276 20.391 9.92528 22.949 13.2516 24.8456C13.7892 25.1619 14.2003 25.4254 14.5165 25.6363C14.8328 25.8471 15.0225 25.9631 15.1596 26.0369C15.2966 26.1107 15.5601 25.7944 15.8131 25.6363C16.0661 25.4782 16.5406 25.1619 17.0782 24.8456C20.403 22.951 23.3023 20.3926 25.5959 17.3294C26.5912 16.1013 27.3223 14.6809 27.7431 13.1572C28.1639 11.6334 28.2655 10.0392 28.0415 8.47434C27.9225 7.56922 27.6166 6.69876 27.1429 5.91835C26.6692 5.13795 26.0382 4.46469 25.2901 3.9414C24.4911 3.41381 23.5932 3.05394 22.651 2.88361C21.7087 2.71328 20.7417 2.73603 19.8085 2.95048C18.3704 3.26278 17.0376 3.94189 15.9396 4.92178L15.3599 5.42779L14.6958 5.0272C14.4006 4.84799 14.1054 4.66878 13.8207 4.47903C13.2948 4.12257 12.7455 3.80207 12.1763 3.51974C11.1433 3.01935 10.0138 2.74958 8.86617 2.72911H8.83451Z"
                fill="#424242"
              />
            </svg>
          </Link>

          <Link href="/contact">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="28"
              height="32"
              viewBox="0 0 28 32"
              fill="none"
            >
              <path
                d="M24.7393 21.2254C23.7501 21.3182 22.7645 21.015 21.9985 20.3821C21.634 20.0669 21.3417 19.6768 21.1416 19.2383C20.9415 18.7999 20.8383 18.3235 20.839 17.8415C20.839 16.7241 20.839 15.6067 20.839 14.4893C20.8304 13.9848 20.9361 13.4849 21.1483 13.0272C21.3605 12.5694 21.6736 12.1656 22.0641 11.8461C22.4546 11.5266 22.9124 11.2997 23.4031 11.1824C23.8938 11.065 24.4047 11.0603 24.8975 11.1686L25.3508 11.2319C25.2454 7.48957 19.9534 3.01988 15.1464 2.62984C12.3974 2.35825 9.64101 3.0689 7.36595 4.63577C5.09089 6.20265 3.4442 8.52452 2.71778 11.1897C2.88626 11.201 3.05529 11.201 3.22377 11.1897C3.71144 11.0843 4.2165 11.0894 4.70195 11.2046C5.1874 11.3199 5.64083 11.5424 6.02906 11.8558C6.41729 12.1692 6.73051 12.5656 6.94554 13.0158C7.16057 13.466 7.27201 13.9587 7.27175 14.4577C7.27175 15.5962 7.27175 16.7347 7.27175 17.8732C7.27458 18.767 6.92364 19.6257 6.29556 20.2616C5.66748 20.8976 4.81323 21.2593 3.91943 21.2676C3.47654 21.2704 3.03755 21.1854 2.62771 21.0175C2.21786 20.8496 1.84538 20.6022 1.53171 20.2895C1.21805 19.9768 0.969438 19.6051 0.800277 19.1958C0.631116 18.7865 0.544726 18.3477 0.546127 17.9048C0.438638 15.8525 0.526884 13.7946 0.809671 11.759C1.4394 8.69703 3.1103 5.94775 5.53826 3.97873C7.96623 2.00971 11.0014 0.942525 14.1274 0.958677C17.2533 0.974829 20.2772 2.07332 22.6847 4.06732C25.0921 6.06133 26.7346 8.82773 27.3327 11.896C27.481 12.9439 27.562 14.0003 27.5751 15.0585C27.5751 15.4591 27.5751 15.8597 27.5751 16.2708C27.5566 19.4448 26.418 22.5102 24.3602 24.9267C22.3024 27.3433 19.4575 28.9556 16.3271 29.4796C16.1206 29.4928 15.9215 29.5614 15.7508 29.6783C15.5801 29.7952 15.4442 29.956 15.3573 30.1437C15.0864 30.6275 14.6597 31.0054 14.1468 31.216C13.6338 31.4265 13.0647 31.4573 12.5321 31.3033C12.0145 31.1537 11.5569 30.8452 11.224 30.4215C10.8911 29.9978 10.6997 29.4803 10.6767 28.942C10.6547 28.3885 10.8165 27.8432 11.1369 27.3912C11.4572 26.9393 11.9182 26.6061 12.4478 26.4436C12.96 26.2741 13.5135 26.2771 14.0238 26.4523C14.534 26.6275 14.9728 26.9651 15.2729 27.4134C15.4732 27.7086 15.6313 27.93 16.0635 27.8456C17.9393 27.5319 19.7102 26.7648 21.2222 25.6111C22.7341 24.4574 23.9415 22.9519 24.7393 21.2254ZM5.61676 16.1127C5.61676 15.5962 5.61676 15.0585 5.61676 14.5736C5.63525 14.3388 5.60488 14.1027 5.52771 13.8801C5.45054 13.6575 5.3282 13.4533 5.1683 13.2803C5.0084 13.1074 4.81444 12.9693 4.59862 12.8749C4.3828 12.7805 4.14972 12.7318 3.91415 12.7318C3.67859 12.7318 3.44564 12.7805 3.22982 12.8749C3.01399 12.9693 2.82003 13.1074 2.66013 13.2803C2.50024 13.4533 2.37789 13.6575 2.30072 13.8801C2.22355 14.1027 2.19318 14.3388 2.21167 14.5736C2.21167 15.6278 2.21167 16.682 2.21167 17.6623C2.19318 17.8972 2.22355 18.1333 2.30072 18.3559C2.37789 18.5784 2.50024 18.7826 2.66013 18.9556C2.82003 19.1286 3.01399 19.2666 3.22982 19.361C3.44564 19.4554 3.67859 19.5042 3.91415 19.5042C4.14972 19.5042 4.3828 19.4554 4.59862 19.361C4.81444 19.2666 5.0084 19.1286 5.1683 18.9556C5.3282 18.7826 5.45054 18.5784 5.52771 18.3559C5.60488 18.1333 5.63525 17.8972 5.61676 17.6623C5.6273 17.1669 5.60622 16.6714 5.61676 16.1127ZM25.92 16.1127C25.92 15.5751 25.92 15.0585 25.92 14.4998C25.9314 14.0476 25.764 13.6091 25.4542 13.2795C25.1443 12.95 24.7171 12.7559 24.265 12.7394C24.0406 12.7293 23.8166 12.7651 23.6065 12.8444C23.3964 12.9238 23.2046 13.045 23.0429 13.2009C22.8812 13.3567 22.7529 13.5438 22.6657 13.7508C22.5786 13.9578 22.5345 14.1804 22.5362 14.4049C22.494 15.5645 22.494 16.7241 22.5362 17.8837C22.5374 18.1028 22.5827 18.3195 22.6693 18.5207C22.756 18.722 22.8824 18.9037 23.0407 19.0552C23.1991 19.2066 23.3862 19.3247 23.5911 19.4023C23.796 19.48 24.0144 19.5156 24.2334 19.5071C24.4571 19.5058 24.6784 19.4602 24.8845 19.3729C25.0906 19.2857 25.2775 19.1586 25.4342 18.9989C25.591 18.8392 25.7146 18.65 25.798 18.4424C25.8814 18.2347 25.9228 18.0126 25.92 17.7888C25.9305 17.2407 25.92 16.682 25.92 16.1127Z"
                fill="#424242"
              />
            </svg>
          </Link>

          <CartMenu />
          <Auth />
        </div>
      </div>
    </div>
  );
}

export default Header;
