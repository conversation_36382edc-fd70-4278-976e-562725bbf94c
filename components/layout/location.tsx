"use client";

import React, { useState, ChangeEvent, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DialogOverlay } from "@radix-ui/react-dialog";
import { Icons } from "../presentation/icons";
import { useDebounce } from "@/utils/hooks/useDebounce";
import apiRoutes from "@/utils/api/routes";
import apiUrl from "@/utils/api/routes";
import { useRouter } from "next/navigation";

function Location() {
  const [isShow, setisShow] = useState(false);
  const [value, setValue] = useState<string>("");
  const [places, setplaces] = useState<any>([]);
  const debouncedValue = useDebounce<string>(value, 500);
  const [selectedPlace, setselectedPlace] = useState({
    place: "No location",
    mainPlace: "No location",
  });
  const router = useRouter();

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value);
  };

  useEffect(() => {
    value && getSearch();
  }, [debouncedValue]);

  const getSearch = async () => {
    try {
      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({ key: value }),
      };
      const response = await fetch(
        apiRoutes.client.placeSearch,
        requestOptions,
      );
      const result = await response.json();
      if (result?.status === "SUCCESS") {
        setplaces(result?.data?.addresses);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const selectLocation = (
    place: string,
    placeId: String,
    mainplace: string,
  ) => {
    setselectedPlace({ place: place, mainPlace: mainplace });
    setisShow(false);
  };

  async function getData(latitude: any, longitude: any) {
    try {
      const res = await fetch(apiUrl.client.chooseLocation, {
        method: "post",
        cache: "no-cache",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          latitude: String(latitude),
          longitude: String(longitude),
        }),
      });
      const result = await res.json();
      if (!res.ok || !result?.status) {
        throw new Error("Failed to fetch data");
      }
      const { message } = result;
      if (
        message == "Delivery not available in this region" ||
        message == "Could not fetch postal code for the location"
      ) {
        router.push("/no-delivery");
      } else {
        router.push("/");
      }

      // return data;
    } catch (error) {
      console.log(error);
    }
  }
  return (
    <div>
      <div className="flex gap-2 items-start">
        <div className="flex-shrink-0">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="15"
            height="19"
            viewBox="0 0 15 19"
            fill="none"
          >
            <path
              d="M8.05354 0.0576172C8.56581 0.153207 9.07255 0.276371 9.57155 0.426576C11.1815 0.977072 12.5176 2.12628 13.3028 3.63572C14.0879 5.14517 14.2618 6.89896 13.7882 8.53317C13.2566 10.346 12.4303 12.0591 11.3426 13.6037C10.2884 15.2482 9.10772 16.8506 7.97975 18.4635C7.88488 18.6005 7.75838 18.7059 7.65296 18.8324H7.23128C7.0837 18.6427 6.9045 18.4635 6.76746 18.2632C5.50245 16.4184 4.21635 14.5947 3.00406 12.7288C2.0697 11.3452 1.39789 9.80147 1.02221 8.17475C0.817878 7.30172 0.793608 6.3962 0.950867 5.51348C1.10813 4.63076 1.44362 3.78934 1.93687 3.04058C2.43012 2.29183 3.07078 1.65144 3.81975 1.15851C4.56871 0.665586 5.41028 0.330455 6.29307 0.173577L6.86233 0.0576172H8.05354ZM7.41051 17.2828C7.4772 17.2187 7.53724 17.1481 7.58971 17.072C8.39088 15.944 9.19206 14.8266 9.9616 13.6881C10.9574 12.2988 11.7821 10.7945 12.4178 9.20784C12.868 8.12566 13.0209 6.94285 12.8606 5.78178C12.6402 4.37498 11.883 3.10841 10.7482 2.24823C9.61342 1.38806 8.18936 1.00127 6.77534 1.16915C5.36132 1.33703 4.06743 2.04651 3.1656 3.14848C2.26377 4.25046 1.82423 5.65911 1.93935 7.07841C2.06678 8.22591 2.40259 9.34051 2.93027 10.3674C3.91847 12.2933 5.07557 14.1277 6.38796 15.8491L7.41051 17.2828Z"
              fill="#424242"
            />
            <path
              d="M7.43168 9.94572C6.78253 9.94569 6.14791 9.75356 5.60774 9.39354C5.06757 9.03351 4.64602 8.52169 4.39618 7.92254C4.14634 7.32339 4.07938 6.66371 4.20375 6.02658C4.32811 5.38945 4.63824 4.80337 5.09505 4.34216C5.55187 3.88095 6.13495 3.56523 6.77086 3.43478C7.40677 3.30432 8.06707 3.36496 8.66859 3.60906C9.2701 3.85315 9.78592 4.26979 10.1511 4.80649C10.5163 5.34319 10.7145 5.97595 10.7207 6.62507C10.7249 7.05965 10.6429 7.49074 10.4795 7.89344C10.3161 8.29614 10.0745 8.66246 9.76864 8.97123C9.4628 9.28 9.0988 9.5251 8.69769 9.69236C8.29657 9.85963 7.86628 9.94574 7.43168 9.94572ZM9.6349 6.62507C9.63699 6.18679 9.50894 5.75775 9.26696 5.39231C9.02498 5.02688 8.67998 4.74149 8.27565 4.57232C7.87133 4.40315 7.42587 4.3578 6.99575 4.44203C6.56563 4.52625 6.1702 4.73626 5.85955 5.04543C5.54889 5.35461 5.33701 5.74904 5.25073 6.17875C5.16445 6.60847 5.20767 7.05412 5.37491 7.45925C5.54215 7.86438 5.82589 8.21074 6.19017 8.45446C6.55444 8.69818 6.98286 8.82829 7.42115 8.82829C8.00561 8.82555 8.56549 8.59287 8.97976 8.18058C9.39402 7.76829 9.62937 7.20951 9.6349 6.62507Z"
              fill="#424242"
            />
          </svg>
        </div>
        <div className="w-full cursor-pointer" onClick={() => setisShow(true)}>
          <div className="text-sm text-dark-500 font-medium flex items-center gap-3">
            {selectedPlace?.mainPlace}{" "}
            <Icons.downArrow className="h-3 w-3" />{" "}
          </div>
          <div
            className="text-dark-500 text-[12px] font-light rounded-lg block w-full "
            onClick={() => setisShow(true)}
          >
            {selectedPlace?.place}
          </div>
        </div>
      </div>

      <Dialog open={isShow} modal={true} onOpenChange={() => setisShow(false)}>
        <DialogContent className="min-w-[60vw] h-auto top-[54vh] ">
          <DialogOverlay className="bg-background/10" />
          <DialogHeader>
            <DialogTitle className="text-lg font-semibold text-black">
              Choose delivery location
            </DialogTitle>
          </DialogHeader>
          <div>
            <form>
              <label className="mb-2 text-sm font-medium text-gray-900 sr-only ">
                Search
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                  <Icons.search />
                </div>
                <input
                  onChange={handleChange}
                  type="search"
                  id="search"
                  className="block w-full p-4 ps-10 text-sm text-gray-900  rounded-lg bg-gray-50"
                  placeholder="Search your City or Pincode"
                />
                <button
                  type="submit"
                  className=" text-gray-500 absolute end-2.5 bottom-2.5  focus:ring-4 focus:outline-none  font-medium rounded-lg text-sm  flex items-center gap-4"
                >
                  <Icons.location />
                  <p>Use Current Location</p>
                </button>
              </div>
            </form>
            <div className="grid grid-cols-1 divide-y  mt-3 overflow-auto h-[10-vh]">
              {places?.map((item: any, index: any) => (
                <div
                  key={index}
                  onClick={async () => {
                    selectLocation(
                      item?.addressLabel,
                      item?.place_id,
                      item?.city,
                    );
                    await getData(item?.latitude, item?.longitude);
                  }}
                  className="ps-6 py-auto py-3 cursor-pointer hover:bg-slate-100"
                >
                  {item?.addressLabel}
                </div>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default Location;
