import Image from "next/image";
import Link from "next/link";
import React from "react";
import { Icons } from "../presentation/icons";

async function Footer() {
  return (
    <div className="w-full bg-gray-900">
      <div className="container py-12">
        <div className="grid grid-cols-12 gap-8">
          {/* Left Section - Logo, Contact Info, Social Media */}
          <div className="col-span-4 border-r border-gray-600 pr-8">
            {/* Ficean Logo */}
            <div>
              <Image
                src="/images/logo/logo-white.png"
                alt="footer-logo"
                width={135}
                height={99}
              />

              <div className="flex justify-between items-end mt-10 mb-4">
                {/* Email Us */}
                <div>
                  <h3 className="text-gray-400 text-sm mb-2">Email Us</h3>
                  <p className="text-white text-sm">{"<EMAIL>"}</p>
                </div>
                {/* Social Media Icons */}
                <div className="flex gap-3 items-center">
                  <a
                    href={"https:instagram.com"}
                    className="text-white hover:text-blue-400"
                  >
                    <Icons.insta />
                  </a>
                  <a
                    href="https://linkedin.com"
                    className="text-white hover:text-blue-400"
                  >
                    <Icons.linkedin />
                  </a>
                  <a
                    href={"https:x.com"}
                    className="text-white hover:text-blue-400"
                  >
                    <Icons.x />
                  </a>
                  <a
                    href={"https:facebook.com"}
                    className="text-white hover:text-blue-400"
                  >
                    <Icons.fb />
                  </a>
                </div>
              </div>

              {/* Call Us */}
              <div className="mb-6">
                <h3 className="text-gray-400 text-sm mb-2">Call Us</h3>
                <p className="text-white text-sm">{"0456 1234567"}</p>
                <p className="text-white text-sm">{"+91 987 654 3210"}</p>
              </div>
            </div>
          </div>

          {/* Middle Section 1 - Projects */}
          <div className="col-span-2">
            <h3 className="text-primary-100 text-base font-semibold mb-4">
              Projects
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/terms/about-us"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/terms/privacy-policy"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms/terms-and-condition"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  Terms & Conditions
                </Link>
              </li>
              <li>
                <Link
                  href="/terms/shipping-policy"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  Shipping Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms/return-policy"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  Return Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms/cancellation-policy"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  Cancellation Policy
                </Link>
              </li>
            </ul>
          </div>

          {/* Middle Section 2 - Explore Ficean */}
          <div className="col-span-2">
            <h3 className="text-primary-100 text-base font-semibold mb-4">
              Explore Ficean
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/blog"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  Blogs
                </Link>
              </li>
              <li>
                <Link
                  href="/news"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  News
                </Link>
              </li>
              <li>
                <Link
                  href="/faq"
                  className="text-gray-300 hover:text-white text-sm"
                >
                  FAQs
                </Link>
              </li>
            </ul>
          </div>

          {/* Right Section - App Download & Newsletter */}
          <div className="col-span-4">
            {/* App Download Section */}
            <div className="flex items-center gap-4 mb-8">
              <Image
                src="/images/logo/play-store.png"
                alt="Get it on Google Play"
                width={200}
                height={42}
                className="cursor-pointer hover:opacity-80"
              />
              <Image
                src="/images/logo/app-store.png"
                alt="Download on the App Store"
                width={200}
                height={42}
                className="cursor-pointer hover:opacity-80"
              />
            </div>

            {/* Newsletter Subscription */}
            <div>
              <h3 className="text-dark-450 text-base font-semibold mb-4">
                Subscribe To Our Newsletter
              </h3>
              <div className="relative  w-full max-w-md">
                <input
                  type="email"
                  placeholder="Your Email Address"
                  className="w-full px-4 py-3 pr-28 bg-[#494949] text-base text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400"
                />
                <button className="absolute top-1/2 -translate-y-1/2 right-1 px-5 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Footer;
