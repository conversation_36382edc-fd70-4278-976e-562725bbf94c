"use client";

import React, { useEffect, useState, memo, useRef } from "react";
import { X } from "lucide-react";
import { useFetch } from "@/utils/hooks/useFeatch";
import apiRoutes from "@/utils/api/routes";
import { useCartStore } from "@/utils/store/cart";
import EmptyCart from "../presentation/emptyCart";
import { Cart } from "@/utils/types/common";
import { useRouter } from "next/navigation";
import CartItems from "../page/checkout/cartItems";
import {
  getCurrentToken,
  setupAuthStateListener,
} from "@/utils/auth/authEvents";

interface CartDrawerProps {
  children: React.ReactNode;
}

const CartDrawer = memo(function CartDrawer({ children }: CartDrawerProps) {
  const router = useRouter();
  const cartStore = useCartStore();
  const itemCount = cartStore.getCartCount() || 0;
  const [isRealod, setIsRealod] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [token, setToken] = useState<string | undefined>(undefined);
  const [isTokenLoaded, setIsTokenLoaded] = useState(false);
  const [totalAmount, setTotalAmount] = useState(0);
  const drawerRef = useRef<HTMLDivElement>(null);

  // Function to check and update token
  const checkToken = () => {
    const accessToken = getCurrentToken();
    setToken(accessToken);
    setIsTokenLoaded(true);
  };

  useEffect(() => {
    // Initial token check
    checkToken();

    // Set up auth state listener
    const cleanup = setupAuthStateListener(checkToken);

    return cleanup;
  }, []);

  // Handle outside click and escape key to close drawer
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        drawerRef.current &&
        !drawerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscapeKey);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        document.removeEventListener("keydown", handleEscapeKey);
      };
    }
  }, [isOpen]);

  const { data } = useFetch<any[]>({
    url: apiRoutes.client.getCartList,
    token: token || undefined,
    start: isTokenLoaded && !!token,
    dep: isRealod,
  });

  // Trigger API call when drawer opens
  useEffect(() => {
    if (isOpen && isTokenLoaded && !!token) {
      setIsRealod(!isRealod);
    }
  }, [isOpen, isTokenLoaded, token]);

  useEffect(() => {
    let calculatedTotal = 0;
    data?.forEach((product) => {
      const price = product.offerPrice ?? product.mrp;
      product.cartDetails.forEach((cart: Cart) => {
        calculatedTotal += price * cart.quantity;
      });
    });
    setTotalAmount(calculatedTotal);
  }, [data]);

  // Custom drawer that appears below header
  if (isOpen) {
    return (
      <>
        <div onClick={() => setIsOpen(true)}>{children}</div>

        {/* Backdrop overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-[9998]"
          onClick={() => setIsOpen(false)}
        />

        {/* Drawer content */}
        <div
          ref={drawerRef}
          className="fixed right-0 top-20 h-[calc(100vh-4rem)] w-full sm:max-w-md bg-gray-50 shadow-lg z-[9999] transform transition-transform duration-300 ease-in-out"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-primary-100 text-white p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={() => setIsOpen(false)}
                className="text-white hover:text-gray-200"
              >
                <X size={20} />
              </button>
              <h2 className="text-lg font-semibold">
                My Bag
                <span className="pl-2 font-normal">({itemCount} Items)</span>
              </h2>
            </div>
          </div>

          {!!data?.length ? (
            <>
              {/* Items count banner */}
              <div className="mx-4 mt-4 text-center">
                <span className="bg-primary-200 rounded-full py-2 px-10 text-white text-xs font-medium">
                  There are {itemCount} items in your bag
                </span>
              </div>

              {/* Cart Items */}
              <CartItems
                data={data}
                isRealod={isRealod}
                setIsRealod={setIsRealod}
                token={token}
              />

              {/* Footer */}
              <div className="absolute bottom-0 left-0 right-0 bg-white border-t p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-dark-450">
                      {itemCount} items selected
                    </p>
                    <p className="text-xl font-semibold">
                      <span className="text-sm font-medium text-[#848484]">
                        Subtotal:{" "}
                      </span>
                      ₹{totalAmount?.toFixed(2)}
                    </p>
                  </div>
                  <button
                    onClick={() => {
                      router.push("/checkout");
                      setIsOpen(false);
                    }}
                    className="bg-primary-100 text-sm text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  >
                    Proceed to Checkout
                  </button>
                </div>
              </div>
            </>
          ) : (
            <EmptyCart setIsOpen={setIsOpen} />
          )}
        </div>
      </>
    );
  }

  // Return trigger when drawer is closed
  return <div onClick={() => setIsOpen(true)}>{children}</div>;
});

export default CartDrawer;
