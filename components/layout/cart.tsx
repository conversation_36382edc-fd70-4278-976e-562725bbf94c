"use client";

import React, { useEffect, useState, memo } from "react";
import { useCartStore } from "@/utils/store/cart";
import { Icons } from "../presentation/icons";
import CartDrawer from "./cartDrawer";

const CartMenu = memo(function CartMenu() {
  const cartStore = useCartStore();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const itemCount = isMounted ? cartStore.getCartCount() : 0;

  return (
    <div>
      <CartDrawer>
        <button className="relative inline-flex items-center p-2 text-sm font-medium text-center text-white rounded-lg outline-none focus:outline-none">
          <div>
            <Icons.cart />
            <span className="sr-only">Cartes</span>
            {isMounted && !!cartStore?.carts?.length && (
              <div className="absolute inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-red-500 border-2 border-white rounded-full -top-1 -end-1">
                {itemCount || 0}
              </div>
            )}
          </div>
        </button>
      </CartDrawer>
    </div>
  );
});

export default CartMenu;
