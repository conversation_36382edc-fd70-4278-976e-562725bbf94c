"use client";

import React, { useEffect, useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import MobileLogin from "../page/auth/mobile";
import Otp from "../page/auth/otp";
import Link from "next/link";
import { Icons } from "../presentation/icons";

function Auth() {
  const [isOpen, setisOpen] = useState(false);
  const [phone, setphone] = useState("");

  const [token, setToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
  }, []);

  const handleOtpSent = ({ phone }: { phone: string }): any => {
    setphone(phone);
  };
  useEffect(() => {
    return () => {
      setphone("");
    };
  }, []);

  return (
    <div>
      {token ? (
        <Link href="/profile">
          <Icons.auth />{" "}
        </Link>
      ) : (
        <button onClick={() => setisOpen(true)}>
          <Icons.auth />{" "}
        </button>
      )}

      <Dialog
        open={isOpen}
        onOpenChange={() => {
          setisOpen(false);
          setphone("");
        }}
      >
        <DialogContent className="sm:max-w-[50vw]  p-0">
          <div className="w-full grid grid-cols-2 h-min-[50vh]">
            <div className=" bg-[url('/images/bg/login.png')] bg-no-repeat bg-cover bg-center"></div>

            <div className="p-10 h-auto">
              <h2 className="text-[2rem]  font-semibold text-dark-50">
                Log in
              </h2>
              <h3 className="mt-5 text-[1.4rem] font-normal text-dark-500">
                Hello !
              </h3>
              <h2 className="text-2xl font-medium text-dark-50">
                Welcome back
              </h2>

              <div>
                {phone ? (
                  <Otp phone={phone} close={() => setisOpen(false)} />
                ) : (
                  <MobileLogin handleOtp={handleOtpSent} />
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default Auth;
