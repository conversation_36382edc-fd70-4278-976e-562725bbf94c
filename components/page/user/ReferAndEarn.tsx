import React from "react";

const referHistory = [
  {
    date: "12 July 2023, 10:30 am",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit,",
    amount: 50,
  },
  {
    date: "12 July 2023, 10:30 am",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit,",
    amount: 50,
  },
  {
    date: "12 July 2023, 10:30 am",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit,",
    amount: 50,
  },
];

const ReferAndEarn = () => {
  return (
    <div className="bg-white rounded-lg shadow border border-gray-200 p-6 min-h-[400px]">
      <div className="text-center mb-4">
        <div className="text-[#3092D0] font-semibold text-sm mb-2">
          EVERY TIME YOU REFER A FRIEND, EVERYBODY WINS!
        </div>
        <p className="text-gray-500 text-sm mb-4 max-w-2xl mx-auto">
          Lorem Ipsum is simply dummy text of the printing and typesetting
          industry. Lorem Ipsum has been the industry's standard dummy text ever
          since the 1500s, when an unknown printer took a galley of type and
          scrambled it to make a type specimen book.
        </p>
        <button className="bg-gradient-to-r from-[#3092D0] to-[#59C4C2] text-white px-6 py-2 rounded-lg font-semibold mb-4">
          Start Referring, Start Earning!
        </button>
        <div className="text-[#3092D0] font-semibold text-sm mb-2">
          Here is How it Works
        </div>
      </div>
      <div className="flex flex-col md:flex-row gap-6 justify-center items-center mb-6">
        <div className="bg-[#F6F9FB] rounded-lg p-4 w-full max-w-xs text-center">
          <div className="text-[#E03B3B] font-bold text-base mb-2">
            Referral Program
          </div>
          <div className="text-gray-700 text-sm mb-2">
            Refer 1 to 2 Friends
            <br />
            Get 2 Token Rewards
          </div>
          <div className="text-gray-700 text-sm mb-2">
            Refer 3 to 5 Friends
            <br />
            Get 5 VIP Points
          </div>
          <div className="text-gray-700 text-sm mb-2">
            Refer 6 to 10 Friends
            <br />
            Get Ficean Voucher worth 1500.00
          </div>
          <div className="text-gray-700 text-sm">
            & 6 Month VIP subscription
          </div>
        </div>
        <img
          src="/images/other/refer-illustration-1.png"
          alt="Refer Illustration"
          className="w-60 h-40 object-contain"
        />
      </div>
      <div className="flex flex-col md:flex-row gap-6 justify-center items-center mb-6">
        <img
          src="/images/other/refer-illustration-2.png"
          alt="Referral Legend"
          className="w-60 h-40 object-contain"
        />
        <div className="bg-[#E6F7F7] rounded-lg p-4 w-full max-w-xs text-center">
          <div className="text-[#3092D0] font-bold text-base mb-2">
            REFERRAL LEGEND
          </div>
          <div className="text-gray-700 text-sm">
            Be the city's finest referrer! Invite more friends and get a special
            badge and token rewards for the highest referrals in the month. Top
            3 referrers to receive a free voucher worth 750.00.
          </div>
        </div>
      </div>
      <div className="mb-6">
        <div className="text-[#3092D0] font-semibold text-sm mb-2">
          HOW TO REFER
        </div>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="bg-[#F6F9FB] rounded-lg p-4 text-center">
            <div className="font-bold text-dark-50 mb-1">1. SHARE</div>
            <div className="text-gray-700 text-sm">
              Ask your friends to register using your referral code/link. Earn
              more when you refer more people.
            </div>
          </div>
          <div className="bg-[#F6F9FB] rounded-lg p-4 text-center">
            <div className="font-bold text-dark-50 mb-1">
              2. FRIEND MAKES FIRST PURCHASE
            </div>
            <div className="text-gray-700 text-sm">
              Any purchase from your referred friend completes the referral. You
              get your reward instantly.
            </div>
          </div>
          <div className="bg-[#F6F9FB] rounded-lg p-4 text-center">
            <div className="font-bold text-dark-50 mb-1">3. YOU GET REWARD</div>
            <div className="text-gray-700 text-sm">
              Get tokens, points, or vouchers based on the number of successful
              referrals.
            </div>
          </div>
        </div>
      </div>
      <div className="text-center mb-6">
        <div className="text-gray-700 text-sm mb-2">
          That's all! Just 3 easy steps to set you off on your path to become a
          referral legend. Invite now!
        </div>
        <button className="bg-gradient-to-r from-[#3092D0] to-[#59C4C2] text-white px-6 py-2 rounded-lg font-semibold">
          Start Referring, Start Earning!
        </button>
      </div>
      <div className="mb-6">
        <div className="divide-y divide-gray-100">
          {referHistory.map((item, idx) => (
            <div key={idx} className="flex items-center justify-between py-4">
              <div>
                <div className="text-xs text-gray-400 mb-1">{item.date}</div>
                <div className="text-sm text-dark-500">{item.description}</div>
              </div>
              <div className="text-lg font-semibold text-green-500">
                +{item.amount.toFixed(2)}
              </div>
            </div>
          ))}
        </div>
        <div className="text-right mt-2">
          <button className="text-[#3092D0] text-sm font-semibold">
            Show More
          </button>
        </div>
      </div>
      <div className="mt-6">
        <div className="text-base font-semibold text-dark-50 mb-2">
          Terms and conditions
        </div>
        <p className="text-gray-500 text-sm">
          Lorem Ipsum is simply dummy text of the printing and typesetting
          industry. Lorem Ipsum has been the industry's standard dummy text ever
          since the 1500s, when an unknown printer took a galley of type and
          scrambled it to make a type specimen book. It has survived not only
          five centuries, but also the leap into electronic typesetting,
          remaining essentially unchanged.
        </p>
      </div>
    </div>
  );
};

export default ReferAndEarn;
