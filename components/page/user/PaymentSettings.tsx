import React, { useState } from "react";

const PaymentSettings = () => {
  // Dummy card data
  const [cards, setCards] = useState([
    {
      id: 1,
      name: "<PERSON>",
      number: "XXXX XXXX XXXX 0813",
      expiry: "00/00",
      type: "visa",
      selected: true,
    },
  ]);

  const [showAddCard, setShowAddCard] = useState(false);

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200 p-6 min-h-[300px]">
      <h2 className="text-xl font-semibold text-dark-50 mb-4">Contact Us</h2>
      <div className="mb-4">
        <div className="text-base font-semibold text-dark-50 mb-2">
          Choose Card
        </div>
        <div className="max-w-md">
          {cards.map((card) => (
            <div
              key={card.id}
              className={`relative rounded-xl border p-6 flex flex-col gap-2 mb-4 ${card.selected ? "border-[#3092D0]" : "border-gray-300"}`}
            >
              <div className="flex justify-between items-center">
                <span className="text-dark-50 font-semibold text-base">
                  {card.name}
                </span>
                {card.selected && (
                  <span className="text-green-500 text-xl font-bold">
                    &#10003;
                  </span>
                )}
              </div>
              <div className="text-lg tracking-widest text-gray-700 font-mono">
                {card.number}
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-gray-400 text-xs">{card.expiry}</span>
                <span className="text-blue-600 font-bold text-lg">
                  {card.type.toUpperCase()}
                </span>
              </div>
            </div>
          ))}
        </div>
        <button
          className="bg-[#3092D0] text-white px-6 py-2 rounded-lg font-semibold block mx-auto mt-2"
          onClick={() => setShowAddCard(true)}
        >
          + Add new card
        </button>
      </div>
      <div className="flex justify-end">
        <button className="bg-[#233D7B] text-white px-8 py-2 rounded-lg font-semibold">
          Save
        </button>
      </div>
      {/* Add card modal can be implemented here if needed */}
    </div>
  );
};

export default PaymentSettings;
