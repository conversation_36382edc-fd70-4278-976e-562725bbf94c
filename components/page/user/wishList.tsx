"use client";

import React, { useEffect, useState } from "react";
import ProductCard from "@/components/main/productCard";
import { useFetch } from "@/utils/hooks/useFeatch";
import apiRoutes from "@/utils/api/routes";
import Spinner from "@/components/presentation/spinner";
import EmptyWishlist from "@/components/presentation/emptyWishlist";

function Wishlist() {
  const [token, setToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
  }, []);

  const { data, isLoading } = useFetch<any>({
    url: apiRoutes.client.wishLists,
    token: token,
    start: !!token,
  });

  return (
    <div>
      {!!data?.data?.length ? (
        <div className="mt-7 grid gap-3 grid-cols-4">
          {data?.data?.map((item: any, number: number) => (
            <ProductCard key={number} data={item} />
          ))}
        </div>
      ) : isLoading ? (
        <div>
          <Spinner />{" "}
        </div>
      ) : (
        <EmptyWishlist />
      )}
    </div>
  );
}

export default Wishlist;
