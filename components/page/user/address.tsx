import { Icons } from "@/components/presentation/icons";
import React, { useState } from "react";
import apiRoutes from "@/utils/api/routes";
import { useFetch } from "@/utils/hooks/useFeatch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import CreateAddress from "../checkout/createAddress";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

function ProfileAddress({ token }: { token: string }) {
  const [isReload, setisReload] = useState(false);
  const [isEdit, setisEdit] = useState<any>({});
  const [isCreate, setisCreate] = useState(false);
  const [deleteId, setdeleteId] = useState("");
  const [isDeleting, setisDeleting] = useState(false);

  const { data } = useFetch<any>({
    url: apiRoutes.client.getAllAddress,
    token,
    start: token,
    dep: isReload,
  });

  const update = () => {
    setisReload(!isReload);
    setisEdit({});
    setisCreate(false);
  };

  const removeAddress = async () => {
    try {
      setisDeleting(true);
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      token && headers.append("Authorization", `Bearer ${token}`);
      const requestConfig: RequestInit = {
        method: "Post",
        headers: headers,
        body: JSON.stringify({ id: deleteId }),
      };
      const response = await fetch(
        apiRoutes.client.removeAddress,
        requestConfig,
      );
      const result = await response.json();
      if (result?.status) {
        setisDeleting(false);
        setdeleteId("");
        update();
      }
    } catch (error) {
      setisDeleting(false);
      setdeleteId("");
    }
  };

  return (
    <div className="divide-y">
      {data?.data?.map((item: any, index: number) => (
        <div key={index} className="flex justify-between items-start py-5">
          <div>
            <h4 className="text-sm font-semibold text-dark-500">
              {item?.name}
            </h4>
            <p className="w-3/5 mt-3 text-sm font-normal">{item?.address}</p>
            <p className="w-3/5 mt-3 text-sm font-normal">
              {item?.landmark} {item?.city} {item?.pincode} {item?.phone}
            </p>
          </div>
          <div className="flex gap-2">
            <button onClick={() => setisEdit(item)}>
              <Icons.edit className="h-4 w-4" />
            </button>
            <button onClick={() => setdeleteId(item?.id)}>
              <Icons.trash />
            </button>
          </div>
        </div>
      ))}

      {!isCreate && (
        <div className="mt-3">
          <button
            onClick={() => setisCreate(true)}
            className="bg-[#FBB03B] py-2 px-5 text-sm font-medium text-dark-500 mt-5 rounded-lg"
          >
            + Add new address
          </button>
        </div>
      )}

      <Sheet open={isCreate} onOpenChange={() => setisCreate(false)}>
        <SheetContent className="bg-white dark:bg-white w-96 overflow-y-auto">
          <SheetHeader className="bg-white">
            <SheetTitle className="text-dark-50 dark:text-dark-50">
              Add address
            </SheetTitle>
          </SheetHeader>
          <div>
            <CreateAddress update={update} />
          </div>
        </SheetContent>
      </Sheet>

      <Sheet open={isEdit?.id} onOpenChange={() => setisEdit({})}>
        <SheetContent className="bg-white dark:bg-white w-96 overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="text-dark-50 dark:text-dark-50">
              Edit Address
            </SheetTitle>
          </SheetHeader>
          <CreateAddress update={update} data={isEdit} />
        </SheetContent>
      </Sheet>

      <AlertDialog open={!!deleteId}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your
              address from account and remove your address data from our
              servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setdeleteId("")}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              disabled={isDeleting}
              onClick={removeAddress}
              className="bg-red-600"
            >
              {isDeleting ? <Icons.loading /> : <p>Delete </p>}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

export default ProfileAddress;
