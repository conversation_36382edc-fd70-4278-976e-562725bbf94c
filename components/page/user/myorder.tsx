import { Icons } from "@/components/presentation/icons";
import React from "react";
import Image from "next/image";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import apiRoutes from "@/utils/api/routes";
import { useFetch } from "@/utils/hooks/useFeatch";
import { Order } from "@/utils/types/common";
import CONSTANT from "@/utils/constant";
import TimeLine from "@/components/main/timeline";
import Link from "next/link";
import { convertToGrams } from "@/utils/helper";
import { downloadInvoice, handleDownloadClick } from "@/utils/downloadHelper";

function Myorder({ token }: { token: string }) {
  const { data, isLoading } = useFetch<any>({
    url: apiRoutes.client.myOrder,
    token,
    start: true,
  });

  const cancelOrder = async (id: string) => {
    console.log("cancell clicked", id);
  };

  return (
    <div>
      {data?.data?.map((item: any, index: number) => (
        <div key={index}>
          <Link href={`/order/${item?.id}`}>
            <div className="bg-white border shadow-sm rounded-xl p-4 md:p-5 cursor-pointer mb-5 hover:border-red-50">
              <div className="flex justify-between items-center gap-5 w-full">
                <div className="w-6/12">
                  <div className="flex justify-start gap-5">
                    <div>
                      <Image
                        className="rounded-lg aspect-squar"
                        width={140}
                        height={135}
                        alt="product"
                        src={item?.items[0]?.product?.images?.[0] || ""}
                      />
                    </div>
                    <div className="text-start">
                      <h4 className=" text-sm font-medium text-dark-500">
                        {item?.items[0]?.title} |{" "}
                        {item?.items[0]?.product?.weight}
                      </h4>
                      <div>
                        <span className="bg-green-700 py-0.5 rounded px-3 text-white text-xs font-normal">
                          {item?.items[0]?.product?.averageRating} ★
                        </span>
                      </div>
                      <span className="text-[#8C8C8C] text-[0.85rem] font-normal">
                        Qty :{" "}
                        {convertToGrams(
                          item?.items[0]?.product?.weight,
                          item?.items[0]?.quantity,
                        )}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="w-2/12 text-center">
                  <h4 className="text-base text-dark-500 font-semibold">
                    {" "}
                    ₹ {item?.totalAmount}
                  </h4>
                  {/* <span className="text-[0.7rem] font-normal text-dark-450">3 offer applied</span> */}
                </div>

                <div className="w-4/12 flex flex-col justify-start items-center gap-4">
                  <p className="text-[0.7rem] font-normal text-dark-500 flex gap-2">
                    {item?.orderStatus == CONSTANT.ORDER_STATUS.DELIVERED ? (
                      <Icons.greenCheck />
                    ) : (
                      <Icons.processing />
                    )}
                    {CONSTANT.ORDER_STATUS_INFO[item?.orderStatus]}
                    {item?.statusHistory?.delivered_on && (
                      <span>
                        Delivered ON{" "}
                        {new Date(
                          item?.statusHistory?.delivered_on,
                        )?.toLocaleDateString()}
                      </span>
                    )}
                  </p>
                  {item?.orderStatus == CONSTANT.ORDER_STATUS.DELIVERED && (
                    <p className="text-[10px] font-light text-dark-450">
                      Your item has been delivered
                    </p>
                  )}

                  {/* Download Invoice Button */}
                  {item?.invoice?.url && (
                    <button
                      onClick={(e) =>
                        handleDownloadClick(e, () =>
                          downloadInvoice(item.invoice.url, item.id),
                        )
                      }
                      className="text-[#195DAF] w-fit font-medium text-[0.65rem] py-2 px-3 bg-[#D2E9FF] rounded-sm flex gap-2 items-center hover:bg-[#C1E0FF] transition-colors"
                    >
                      <Icons.invoice /> Download Invoice
                    </button>
                  )}
                </div>
              </div>
            </div>
          </Link>
        </div>
      ))}
    </div>
  );
}

export default Myorder;
