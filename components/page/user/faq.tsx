"use client";

import React, { useState, useEffect } from "react";
import { useFetch } from "@/utils/hooks/useFeatch";
import apiRoutes from "@/utils/api/routes";
import apiUrl from "@/utils/api/routes";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Spinner from "@/components/presentation/spinner";

function Faq({ page }: { page: string }) {
  const [selectedTag, setSelectedTag] = useState("");
  const [data, setData] = useState<any>();
  const { data: faqData, isLoading } = useFetch<any>({
    url: apiRoutes.client.getFaq,
    options: { body: JSON.stringify({ type: "user" }) },
    start: true,
  });

  // const url: any = {
  //   "terms-and-condition": apiUrl.client.getTermsAndCondition,
  //   "cancellation-policy": apiUrl.client.getCancellationPolicy,
  //   "shipping-policy": apiUrl.client.getShippingPolicy,
  //   "privacy-policy": apiUrl.client.getPrivacyPolicy,
  //   "refund-policy": apiUrl.client.getRefundPolicy,
  //   "return-policy": apiUrl.client.getReturnPolicy,
  //   "about-us": apiUrl.client.getAboutUs,
  // };

  // useEffect(() => {
  //   async function getData() {
  //     try {
  //       const res = await fetch(url[slug], {
  //         method: "post",
  //         cache: "no-cache",
  //         headers: {
  //           "Content-Type": "application/json",
  //         },
  //         body: JSON.stringify({ limit: 100 }),
  //       });
  //       const result = await res.json();
  //       setData(result);
  //       if (!res.ok || !result?.status) {
  //         throw new Error("Failed to fetch data");
  //       }
  //     } catch (error) {
  //       console.log(error);
  //     }
  //   }
  //   getData();
  // }, [slug]);

  const filterData = faqData?.data?.filter((item: any) => {
    if (!selectedTag) return true;
    return item?.tag === selectedTag;
  });

  return (
    <div className="p-5">
      {page === "main" && (
        <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
          <h1 className="text-dark-50 font-semibold text-[1.4rem]">
            Frequently asked questions
          </h1>
        </div>
      )}
      <div className="flex gap-4">
        {faqData?.tags?.map((item: string, index: number) => (
          <button
            key={index}
            onClick={() => setSelectedTag(item)}
            className={`text-xs font-normal ${selectedTag == item ? "bg-[#3F8DCC] font-semibold text-white" : "text-dark-450 border-[#969696] border"}   py-2 px-4 rounded-2xl`}
          >
            {item}
          </button>
        ))}
      </div>

      {!isLoading ? (
        <div className="mt-2">
          <Accordion type="single" collapsible className="w-full">
            {filterData?.map((item: any, index: number) => (
              <div key={index}>
                {" "}
                <AccordionItem value={index.toString()}>
                  <AccordionTrigger className="hover:no-underline">
                    {item?.question}
                  </AccordionTrigger>
                  <AccordionContent>{item?.ans}</AccordionContent>
                </AccordionItem>
              </div>
            ))}
          </Accordion>
        </div>
      ) : (
        <Spinner />
      )}
      {/* 
      <div>
        <div className="w-full flex justify-center items-center mb-7">
          <h2 className="text-2xl font-semibold uppercase">
            {data?.data?.title || ""}
          </h2>
        </div>
        {data?.data?.content && (
          <div dangerouslySetInnerHTML={{ __html: data?.data?.content }} />
        )}
      </div> */}
    </div>
  );
}

export default Faq;
