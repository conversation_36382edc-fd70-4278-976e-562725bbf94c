import { User } from "@/utils/types/user";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import apiRoutes from "@/utils/api/routes";
import { Icons } from "@/components/presentation/icons";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";

interface Inputs {}

function UserProfile({
  data,
  token,
  update,
}: {
  data?: User | null;
  token: string;
  update: () => void;
}) {
  const [isLoading, setisLoading] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<User>();
  const [image, setImage] = useState<any>(null);
  const router = useRouter();
  const { toast } = useToast();

  const updateProfile = async (data: Inputs) => {
    try {
      setisLoading(true);
      const header = new Headers();
      header.append("Authorization", `Bearer ${token}`);
      header.append("Content-Type", "application/json");
      const response = await fetch(apiRoutes.client.updateProfile, {
        headers: header,
        method: "POST",
        body: JSON.stringify(data),
      });
      const result = await response.json();
      if (result?.status) {
        update();
        toast({ title: "Profile changed successfully" });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setisLoading(false);
    }
  };

  const updateImage = async (image: File) => {
    try {
      const formData = new FormData();
      formData.append("file", image);
      const header = new Headers();
      header.append("Authorization", `Bearer ${token}`);

      const response = await fetch(apiRoutes.client.updateAvatar, {
        headers: header,
        method: "POST",
        body: formData,
      });
      const result = await response.json();
      if (result?.status) {
        router.refresh();
        console.log("Image uploaded successfully");
        // Optionally, handle successful upload response
      } else {
        console.error("Image upload failed");
        // Optionally, handle failed upload response
      }
    } catch (error) {
      console.error("Error uploading image:", error);
    }
  };

  const fileChange = (event: any) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImage(reader.result);
      };
      reader.readAsDataURL(file);
      updateImage(file);
    }
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(updateProfile)}>
        <div className="grid grid-cols-12 items-center gap-5">
          <div className="col-span-8">
            <div className="grid grid-cols-2 gap-5">
              <div className="w-full">
                <label className="block mb-2 text-sm font-medium text-gray-900 ">
                  First Name
                </label>
                <input
                  defaultValue={data?.firstName}
                  {...register("firstName", { required: true })}
                  className="bg-gray-50  text-gray-900 text-sm outline-none block w-full p-2.5"
                  placeholder="Enter frst name"
                />
              </div>
              <div className="w-full">
                <label className="block mb-2 text-sm font-medium text-gray-900 ">
                  Last Name
                </label>
                <input
                  defaultValue={data?.lastName}
                  {...register("lastName", { required: true })}
                  className="bg-gray-50  text-gray-900 text-sm outline-none block w-full p-2.5"
                  placeholder="Enter Last name"
                />
              </div>
              <div className="w-full">
                <label className="block mb-2 text-sm font-medium text-gray-900 ">
                  Mobile
                </label>
                <input
                  defaultValue={data?.mobileNo}
                  {...register("mobileNo", { required: true })}
                  className="bg-gray-50  text-gray-900 text-sm outline-none block w-full p-2.5"
                  placeholder="Enter mobile number"
                />
              </div>
              <div className="w-full">
                <label className="block mb-2 text-sm font-medium text-gray-900 ">
                  Email
                </label>
                <input
                  defaultValue={data?.email}
                  {...register("email", { required: true })}
                  className="bg-gray-50  text-gray-900 text-sm outline-none block w-full p-2.5"
                  placeholder="Enter email id"
                />
              </div>
            </div>
          </div>

          <div className="col-span-4 flex justify-center">
            <div>
              <div className="relative inline-block">
                <Image
                  className="w-24 h-24 rounded-full border-2 border-white"
                  width={100}
                  height={100}
                  src={
                    image || data?.avatar || "/images/icon/userPlaceholder.webp"
                  }
                  alt="avatar"
                />
                <div className="w-7 h-7 flex justify-center items-center rounded-full bg-[#3559A8] absolute bottom-0.5 right-0.5">
                  <label htmlFor="avatar">
                    <Icons.edit className="p-1 fill-white cursor-pointer" />
                  </label>
                </div>
              </div>
            </div>
          </div>

          <input
            onChange={fileChange}
            type="file"
            name="file"
            id="avatar"
            hidden
          />
        </div>
        <div className="mt-5 flex justify-end w-full">
          <button
            disabled={isLoading}
            type="submit"
            className="text-base font-medium text-white py-2 px-5 bg-[#3559A8] rounded-lg disabled:bg-white"
          >
            Save {isLoading && <Icons.loading />}
          </button>
        </div>
      </form>
    </div>
  );
}

export default UserProfile;
