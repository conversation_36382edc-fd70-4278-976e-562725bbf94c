import React from "react";
import Enquiry from "../home/<USER>";
import Image from "next/image";
import { Icons } from "@/components/presentation/icons";

function Contact() {
  return (
    <div>
      <h2 className="text-2xl font-semibold text-white">
        We like to hear from you!
      </h2>
      <div className="mt-2 grid grid-cols-2 items-end gap-5">
        <div>
          <Enquiry />
        </div>
        <div className="flex justify-center items-center">
          <Image
            src="/images/other/contact.png"
            width={350}
            height={300}
            alt="privacy policy"
            quality={100}
          />
        </div>

        <div className="bg-[#3F8DCC] rounded-xl p-12 w-full h-56">
          <div className="  w-full">
            <div className="flex gap-5 items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="28"
                viewBox="0 0 20 28"
                fill="none"
                className="flex-shrink-0"
              >
                <path
                  d="M10.1821 0.741455C8.48292 0.743363 6.81507 1.19875 5.35063 2.06062C3.88619 2.9225 2.67821 4.15965 1.85136 5.64437C1.02451 7.1291 0.608733 8.80764 0.646947 10.5068C0.685161 12.2059 1.17598 13.864 2.06875 15.31L9.62103 27.5152C9.69275 27.6309 9.79285 27.7263 9.91181 27.7924C10.0308 27.8585 10.1646 27.8931 10.3007 27.8929C10.4381 27.8931 10.573 27.8573 10.6923 27.7891C10.8115 27.721 10.9108 27.6228 10.9804 27.5044L18.3601 15.1805C19.2236 13.731 19.6877 12.0785 19.7053 10.3912C19.7228 8.70396 19.2931 7.04217 18.4599 5.57498C17.6267 4.1078 16.4198 2.88759 14.962 2.03856C13.5041 1.18953 11.8474 0.741977 10.1605 0.741455H10.1821ZM17.0223 14.3604L10.3655 25.5728L3.47131 14.4683C2.7274 13.2665 2.31663 11.8884 2.28119 10.4754C2.24574 9.06243 2.5869 7.66546 3.26962 6.42792C3.95234 5.19038 4.95204 4.15687 6.16608 3.43351C7.38011 2.71014 8.76474 2.32298 10.1778 2.31177C11.5909 2.30056 12.9815 2.6657 14.2068 3.36972C15.4322 4.07373 16.4481 5.09124 17.1504 6.31779C17.8527 7.54434 18.2159 8.93575 18.2029 10.3491C18.1898 11.7625 17.801 13.147 17.0762 14.3604H17.0223Z"
                  fill="#FFFFFF"
                />
                <path
                  d="M10.1821 5.51129C9.23676 5.51129 8.31268 5.79166 7.5267 6.31697C6.74071 6.84227 6.12811 7.58891 5.76636 8.46246C5.40461 9.33601 5.30996 10.2972 5.49438 11.2246C5.6788 12.152 6.134 13.0038 6.80243 13.6724C7.47086 14.3409 8.32249 14.7963 9.24962 14.9807C10.1768 15.1652 11.1378 15.0705 12.0111 14.7087C12.8844 14.3468 13.6309 13.7341 14.1561 12.9479C14.6813 12.1617 14.9616 11.2375 14.9616 10.2919C14.9587 9.0249 14.4543 7.81057 13.5585 6.91464C12.6628 6.01871 11.4488 5.51414 10.1821 5.51129ZM10.1821 13.4754C9.53962 13.4753 8.91174 13.284 8.37837 12.9259C7.84499 12.5677 7.43024 12.0588 7.18693 11.4641C6.94362 10.8694 6.88275 10.2157 7.01207 9.58627C7.1414 8.95684 7.45506 8.38012 7.91313 7.92957C8.3712 7.47903 8.95295 7.17504 9.58432 7.05628C10.2157 6.93753 10.8681 7.00938 11.4585 7.26271C12.0489 7.51604 12.5506 7.93938 12.8997 8.47883C13.2488 9.01828 13.4295 9.64943 13.4188 10.2919C13.4188 10.7146 13.3347 11.133 13.1714 11.5228C13.0081 11.9126 12.7688 12.266 12.4676 12.5623C12.1663 12.8586 11.809 13.092 11.4166 13.2488C11.0242 13.4055 10.6045 13.4826 10.1821 13.4754Z"
                  fill="#FFFFFF"
                />
              </svg>
              <p className=" text-[0.82rem] text-white font-medium font-inter justify-center">
                1234 NW Bobcat Lane, St. Robert 1234 NW Bobcat Lane, St. Robert
              </p>
            </div>
          </div>

          <div className="flex gap-5 mx-auto mt-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="18"
              viewBox="0 0 24 18"
              fill="none"
            >
              <path
                d="M21.7174 0.671021H3.00936C2.43708 0.671021 1.88823 0.898378 1.48356 1.30314C1.0789 1.7079 0.851562 2.25691 0.851562 2.82933V15.7792C0.851562 16.3516 1.0789 16.9005 1.48356 17.3053C1.88823 17.71 2.43708 17.9375 3.00936 17.9375H21.7174C22.2897 17.9375 22.8386 17.71 23.2432 17.3053C23.6479 16.9005 23.8752 16.3516 23.8752 15.7792V2.82933C23.8752 2.25691 23.6479 1.7079 23.2432 1.30314C22.8386 0.898378 22.2897 0.671021 21.7174 0.671021ZM21.7174 2.10631C21.8134 2.10752 21.9084 2.12577 21.998 2.16026L12.3634 10.5129L2.72884 2.16026C2.81841 2.12577 2.91339 2.10752 3.00936 2.10631H21.7174ZM21.7174 16.5022H3.00936C2.81764 16.5022 2.63378 16.426 2.49822 16.2904C2.36265 16.1548 2.28649 15.9709 2.28649 15.7792V3.68183L11.8887 12.0129C12.0222 12.1235 12.1901 12.1839 12.3634 12.1839C12.5367 12.1839 12.7046 12.1235 12.8381 12.0129L22.4403 3.68183V15.7792C22.4403 15.9709 22.3641 16.1548 22.2286 16.2904C22.093 16.426 21.9092 16.5022 21.7174 16.5022Z"
                fill="#FFFFFF"
              />
            </svg>
            <span className=" text-[0.82rem] text-white font-medium font-inter">
              <EMAIL>
            </span>
          </div>

          <div className="flex gap-5 mx-auto mt-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="18"
              viewBox="0 0 24 18"
              fill="none"
            >
              <path
                d="M21.7174 0.671021H3.00936C2.43708 0.671021 1.88823 0.898378 1.48356 1.30314C1.0789 1.7079 0.851562 2.25691 0.851562 2.82933V15.7792C0.851562 16.3516 1.0789 16.9005 1.48356 17.3053C1.88823 17.71 2.43708 17.9375 3.00936 17.9375H21.7174C22.2897 17.9375 22.8386 17.71 23.2432 17.3053C23.6479 16.9005 23.8752 16.3516 23.8752 15.7792V2.82933C23.8752 2.25691 23.6479 1.7079 23.2432 1.30314C22.8386 0.898378 22.2897 0.671021 21.7174 0.671021ZM21.7174 2.10631C21.8134 2.10752 21.9084 2.12577 21.998 2.16026L12.3634 10.5129L2.72884 2.16026C2.81841 2.12577 2.91339 2.10752 3.00936 2.10631H21.7174ZM21.7174 16.5022H3.00936C2.81764 16.5022 2.63378 16.426 2.49822 16.2904C2.36265 16.1548 2.28649 15.9709 2.28649 15.7792V3.68183L11.8887 12.0129C12.0222 12.1235 12.1901 12.1839 12.3634 12.1839C12.5367 12.1839 12.7046 12.1235 12.8381 12.0129L22.4403 3.68183V15.7792C22.4403 15.9709 22.3641 16.1548 22.2286 16.2904C22.093 16.426 21.9092 16.5022 21.7174 16.5022Z"
                fill="#FFFFFF"
              />
            </svg>
            <span className=" text-[0.82rem] text-white font-medium font-inter">
              +91 9999 999 999 <br />
              +91 9999 999 999
            </span>
          </div>
        </div>

        <div className="bg-[#FFFFF] border border-primary-100 rounded-xl p-12 w-full h-56">
          <div className="flex justify-center items-center">
            <div className="flex flex-col justify-between items-center">
              <div>
                <p className=" font-semibold text-[1.32rem] text-primary-100">
                  Connect us in
                </p>
              </div>
              <div className="mt-11 flex justify-between gap-3">
                <button className="p-3 rounded-xl hover:bg-white/[0.11]">
                  <Icons.fb />
                </button>
                <button className="p-3 rounded-xl hover:bg-white/[0.11]">
                  <Icons.insta />
                </button>
                <button className="p-3 rounded-xl hover:bg-white/[0.11]">
                  <Icons.x />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Contact;
