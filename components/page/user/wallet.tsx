import React from "react";

// Dummy data for demonstration
const walletBalance = 3500;
const walletHistory = [
  {
    date: "12 July 2023, 10:30 am",
    description: "Lorem ipsum dolor sit amet, consectetuer adipiscing elit,",
    amount: -50,
  },
  {
    date: "12 July 2023, 10:30 am",
    description: "Lorem ipsum dolor sit amet, consectetuer adipiscing elit,",
    amount: 50,
  },
  {
    date: "12 July 2023, 10:30 am",
    description: "Lorem ipsum dolor sit amet, consectetuer adipiscing elit,",
    amount: 50,
  },
  {
    date: "12 July 2023, 10:30 am",
    description: "Lorem ipsum dolor sit amet, consectetuer adipiscing elit,",
    amount: 50,
  },
  {
    date: "12 July 2023, 10:30 am",
    description: "Lorem ipsum dolor sit amet, consectetuer adipiscing elit,",
    amount: -50,
  },
];

const Wallet = () => {
  return (
    <div className="bg-white rounded-lg shadow border border-gray-200 p-6 min-h-[400px]">
      {/* <h2 className="text-xl font-semibold text-dark-50 mb-6">My Wallet</h2> */}
      <div className="flex flex-col items-center mb-8">
        <div className="w-full max-w-xs bg-gradient-to-r from-[#3092D0] to-[#59C4C2] rounded-xl p-6 flex flex-col items-center">
          <span className="text-white text-sm font-medium mb-1">
            Wallet Balance
          </span>
          <span className="text-white text-3xl font-bold">
            Rs.
            {walletBalance.toLocaleString(undefined, {
              minimumFractionDigits: 2,
            })}
          </span>
        </div>
      </div>
      <div>
        <h3 className="text-base font-semibold text-dark-50 mb-4">
          Wallet History
        </h3>
        <div className="divide-y divide-gray-100">
          {walletHistory.map((item, idx) => (
            <div key={idx} className="flex items-center justify-between py-4">
              <div>
                <div className="text-xs text-gray-400 mb-1">{item.date}</div>
                <div className="text-sm text-dark-500">{item.description}</div>
              </div>
              <div
                className={`text-lg font-semibold ${item.amount > 0 ? "text-green-500" : "text-red-500"}`}
              >
                {item.amount > 0 ? "+" : "-"} {Math.abs(item.amount).toFixed(2)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Wallet;
