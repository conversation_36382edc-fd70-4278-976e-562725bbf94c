"use client";

import { useFetch } from "@/utils/hooks/useFeatch";
import { Icms } from "@/utils/types/common";
import React, { useState, useEffect } from "react";
import apiRoutes from "@/utils/api/routes";
import apiUrl from "@/utils/api/routes";
import Image from "next/image";

function TermsAndCondition() {
  const [data, setData] = useState<any>();

  const url: any = {
    "terms-and-condition": apiUrl.client.getTermsAndCondition,
    "cancellation-policy": apiUrl.client.getCancellationPolicy,
    "shipping-policy": apiUrl.client.getShippingPolicy,
    "privacy-policy": apiUrl.client.getPrivacyPolicy,
    "refund-policy": apiUrl.client.getRefundPolicy,
    "return-policy": apiUrl.client.getReturnPolicy,
    "about-us": apiUrl.client.getAboutUs,
  };

  useEffect(() => {
    async function getData() {
      try {
        const res = await fetch(url["terms-and-condition"], {
          method: "post",
          cache: "no-cache",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ limit: 100 }),
        });
        const result = await res.json();
        setData(result);
        if (!res.ok || !result?.status) {
          throw new Error("Failed to fetch data");
        }
      } catch (error) {
        console.log(error);
      }
    }
    getData();
  }, []);

  return (
    <div>
      <div className="grid grid-cols-2 gap-5 items-center">
        <div>
          <p className="mt-5 text-[0.7rem] text-dark-500 font-normal">
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Culpa dolor
            eligendi, officiis laborum explicabo assumenda vero quia cum
            ratione, id accusantium saepe eos, deserunt doloremque ipsum
            perspiciatis repellat autem deleniti? Lorem, ipsum dolor sit amet
            consectetur adipisicing elit. Aspernatur voluptates illum alias
            neque culpa quasi cumque assumenda quas, iste totam quaerat, ratione
            fugit perferendis aut? Recusandae deleniti ea quaerat unde? Lorem
            ipsum dolor sit amet consectetur adipisicing elit. Facere
            consequuntur corrupti neque cum accusantium, expedita praesentium
            fugiat quam fugit incidunt deserunt obcaecati, sint commodi maiores,
            eligendi assumenda suscipit impedit repellendus. Lorem ipsum dolor
            sit amet consectetur adipisicing elit. Repellat aut unde veniam.
            Quisquam laudantium qui aspernatur mollitia officiis similique id
            quam, tempora voluptatem fugiat culpa temporibus perferendis
            voluptatibus, eius dignissimos.
          </p>
        </div>
        <div className="flex justify-center items-start">
          <Image
            src="/images/other/terms.png"
            width={250}
            height={250}
            alt="privacy policy"
            quality={100}
            className="aspect-auto"
          />
        </div>
      </div>
      <div className="mt-14">
        <h5 className="text-2xl font-semibold text-dark-50">
          Customer Registration and Privacy Policy
        </h5>
        <p className="my-5 text-[0.7rem] text-dark-500 font-normal">
          Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam
          nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat
          volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation
          ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat.
          Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse
          molestie consequat
        </p>

        <h5 className="text-2xl font-semibold text-dark-50">Who Can Sign Up</h5>
        <p className="my-5 text-[0.7rem] text-dark-500 font-normal">
          Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam
          nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat
          volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation
          ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat.
          Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse
          molestie consequat
        </p>

        <h5 className="text-2xl font-semibold text-dark-50">
          Terms of access to Platform
        </h5>
        <p className="my-5 text-[0.7rem] text-dark-500 font-normal">
          {" "}
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Odio ea,
          numquam et facilis sapiente earum eligendi debitis minus id, quo iure
          ipsam nam consectetur quisquam maiores unde quae natus! Odit?Lorem
          ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy
          nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.
          Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper
          suscipit lobortis nisl ut aliquip ex ea commodo consequat. Duis autem
          vel eum iriure dolor in hendrerit in vulputate velit esse molestie
          consequat
        </p>

        <h5 className="text-2xl font-semibold text-dark-50">Pricing</h5>
        <p className="my-5 text-[0.7rem] text-dark-500 font-normal">
          Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam
          nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat
          volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation
          ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat.
          Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse
          molestie consequat
        </p>

        <h5 className="text-2xl font-semibold text-dark-50">
          Delivery and Handling Charges
        </h5>
        <p className="my-5 text-[0.7rem] text-dark-500 font-normal">
          Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam
          nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat
          volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation
          ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat.
          Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse
          molestie consequat
        </p>
      </div>
    </div>
  );
}

export default TermsAndCondition;
