"use client";

import React from "react";
import Promocode from "./promocode";
import Address from "./address";
import Timeslot from "./timeslot";
import Summary from "./summary";
import { Icons } from "@/components/presentation/icons";

interface Props {
  token: string | undefined;
  total: number; // This is the subtotal (cart items total)
  finalTotal?: number; // This is the final payable amount
  discount: number;
  platformFee: number;
  deliveryCharge: number;
  promoCode: string;
  setPromoCode: React.Dispatch<React.SetStateAction<string>>;
  address: string;
  setAddress: React.Dispatch<React.SetStateAction<string>>;
  timeslot: string;
  setTimeslot: React.Dispatch<React.SetStateAction<string>>;
  setDiscount: React.Dispatch<React.SetStateAction<number>>;
  placeOrder: () => Promise<void>;
  isPlacing: boolean;
  isProceed: boolean;
  setIsProceed: React.Dispatch<React.SetStateAction<boolean>>;
}

function Delivery({
  token,
  total,
  finalTotal,
  discount,
  platformFee,
  deliveryCharge,
  promoCode,
  setPromoCode,
  address,
  setAddress,
  timeslot,
  setTimeslot,
  setDiscount,
  placeOrder,
  isPlacing,
  isProceed,
  setIsProceed,
}: Props) {
  return (
    <div className="bg-white shadow-md rounded-xl flex flex-col h-full">
      <div className="p-5 flex-1 overflow-y-auto">
        <Promocode
          token={token}
          promoCode={promoCode}
          setPromoCode={setPromoCode}
          setDiscount={setDiscount}
        />

        {!isProceed && (
          <>
            <Address token={token} address={address} setAddress={setAddress} />

            <Timeslot
              token={token}
              timeslot={timeslot}
              setTimeslot={setTimeslot}
            />
          </>
        )}

        <Summary
          total={total}
          finalTotal={finalTotal}
          discount={discount}
          platformFee={platformFee}
          deliveryCharge={deliveryCharge}
        />
      </div>

      {/* Sticky Place Order Button */}
      <div className="sticky bottom-0 bg-white p-5 border-t border-gray-100 rounded-b-xl">
        {address && (
          <button
            disabled={isPlacing}
            onClick={placeOrder}
            className="text-white bg-primary-100 flex gap-3 font-medium rounded-lg text-lg px-5 py-2.5 justify-center items-center w-full disabled:bg-transparent"
          >
            {!isPlacing ? "Place Order" : <Icons.loading />}
          </button>
        )}
      </div>
    </div>
  );
}

export default Delivery;
