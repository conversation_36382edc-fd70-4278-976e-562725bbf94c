import { Address } from "@/utils/types/common";
import React, { useEffect, useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import apiRoutes from "@/utils/api/routes";
import { Icons } from "@/components/presentation/icons";
import { toast } from "react-toastify";

function CreateAddress({
  update,
  data,
}: {
  update: () => void;
  data?: Address;
}) {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Address>();
  const [error, setError] = useState("");
  const [isUpdating, setisUpdating] = useState(false);

  const [token, setToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
  }, []);

  const onSubmit: SubmitHandler<Address> = async (dataToUpdate) => {
    try {
      const newData = { ...dataToUpdate };
      if (data?.id) newData.id = data?.id;
      setisUpdating(true);
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      token && headers.append("Authorization", `Bearer ${token}`);
      const requestConfig: RequestInit = {
        method: "Post",
        headers: headers,
        body: JSON.stringify(newData),
      };
      const url = data?.id
        ? apiRoutes.client.updateAddress
        : apiRoutes.client.addAddress;
      const response = await fetch(url, requestConfig);
      const result = await response.json();
      if (result?.code === 200) {
        setisUpdating(false);
        toast.success("Address Updated!");
        reset();
        update();
        return;
      } else {
        console.log(result?.message);
        setError(result?.message || "Failed to update address");
      }
      setisUpdating(false);
      toast.error("Failed to update address!");
    } catch (error) {
      setisUpdating(false);
    }
  };

  const required = "This field is required";

  return (
    <div>
      <div>
        <div className="mx-auto mt-5">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="pt-5">
              <label className=" my-1 block text-sm font-medium text-gray-900">
                Name
              </label>
              <input
                defaultValue={data?.name}
                {...register("name", {
                  required,
                  minLength: { value: 3, message: "minimum 3 chracter" },
                })}
                className="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5"
              />
              {errors?.name && (
                <span className="text-red-50 text-xs">
                  {errors?.name?.message}
                </span>
              )}
            </div>
            <div className="pt-5">
              <label className=" my-1 block text-sm font-medium text-gray-900">
                Flat / Room No
              </label>
              <input
                defaultValue={data?.address2}
                {...register("address2", {
                  required,
                  minLength: { value: 3, message: "minimum 3 chracter" },
                })}
                className="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5"
              />
              {errors?.address2 && (
                <span className="text-red-50 text-xs">
                  {errors?.address2?.message}
                </span>
              )}
            </div>
            <div className="sm:col-span-2">
              <label className=" mb-1 block text-sm font-medium text-gray-900 ">
                Address
              </label>
              <textarea
                {...register("address", {
                  required,
                  minLength: { value: 3, message: "minimum 3 chracter" },
                })}
                defaultValue={data?.address}
                rows={4}
                className="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg shadow-sm border border-gray-300 focus:ring-primary-500 focus:border-primary-500"
              ></textarea>
              {errors?.address && (
                <span className="text-red-50 text-xs">
                  {errors?.address?.message}
                </span>
              )}
              {error && (
                <div className="mt-2 text-red-500 text-sm">{error}</div>
              )}
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className=" mb-1 block text-sm font-medium text-gray-900 ">
                  Landmark
                </label>
                <input
                  defaultValue={data?.landmark}
                  {...register("landmark", { required })}
                  type="text"
                  id="subject"
                  className="block p-3 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
                {errors?.landmark && (
                  <span className="text-red-50 text-xs">
                    This field is required
                  </span>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className=" mb-1 block text-sm font-medium text-gray-900 ">
                  Phone
                </label>
                <input
                  defaultValue={data?.phone}
                  {...register("phone", { required })}
                  type="text"
                  id="subject"
                  className="block p-3 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
                {errors?.phone && (
                  <span className="text-red-50 text-xs">
                    This field is required
                  </span>
                )}
              </div>
            </div>
            <div className="flex justify-end w-full">
              <button
                disabled={isUpdating}
                type="submit"
                className="py-3 px-5 text-sm font-medium text-center text-white rounded-lg bg-green-600 sm:w-fit hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300"
              >
                {isUpdating ? <Icons.loading /> : "Save"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default CreateAddress;
