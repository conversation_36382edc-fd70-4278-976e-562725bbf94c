import { useState } from "react";
import Image from "next/image";
import apiRoutes from "@/utils/api/routes";
import { useCartStore } from "@/utils/store/cart";
import { Icons } from "@/components/presentation/icons";
import { Minus, Plus, Trash2 } from "lucide-react";
import { convertToGrams, calculateCartItemPrice } from "@/utils/helper";

interface CartItemsProps {
  data: any[];
  isRealod: boolean;
  setIsRealod: React.Dispatch<React.SetStateAction<boolean>>;
  token: string | undefined;
}

interface CartDetail {
  marinationType: any;
  variant: any;
  quantity: number;
  id: string;
}

export default function CartItems({
  data,
  isRealod,
  setIsRealod,
  token,
}: CartItemsProps) {
  const cartStore = useCartStore();
  const [isIncreasing, setisIncreasing] = useState(false);
  const [isDecreasing, setisDecreasing] = useState(false);
  const [isRemoving, setisRemoving] = useState(false);

  function hasPopulatedCartItems(cartDetails: CartDetail[]) {
    return cartDetails.some((item) => item.marinationType || item.variant);
  }

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4 h-[calc(100vh-16rem)]">
      {data.map((item) => (
        <div key={item?._id} className="bg-white rounded-lg p-4 shadow-sm">
          {/* Main Item */}
          <div className="flex gap-3">
            <Image
              src={item?.images[0]}
              alt={item?.title}
              width={130}
              height={100}
              className="w-30 h-20 rounded-lg object-contain"
            />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-800 text-sm">
                {item?.title} | {item?.weight}
              </h3>
              <p className="text-gray-500 font-normal text-xs">
                Base Price :{" "}
                <span className="font-normal">
                  ₹{(item?.offerPrice ?? item?.mrp)?.toFixed(2)}
                </span>
              </p>
              <p className="text-gray-500 font-normal text-xs">
                Qty : <span className="font-normal">1Pack</span>
              </p>

              <div className="flex bg-[#E2E2E2] rounded-sm inline-flex px-3 py-0.5 items-center gap-1 mt-1">
                <span className="text-green-600 text-xs">
                  ★ {item?.averageRating}
                </span>
                <span className="text-gray-400 text-xs">
                  | {item?.totalReviews}
                </span>
              </div>
            </div>
          </div>

          {/* Quantity Controls and Remove */}
          <div className="mt-5">
            {(() => {
              const hasVariantsOrMar = hasPopulatedCartItems(item?.cartDetails);

              const increaseCart = async (
                cartId: string,
                isIn: boolean,
                isDel: boolean,
              ) => {
                try {
                  isIn
                    ? setisIncreasing(true)
                    : isDel
                      ? setisRemoving(true)
                      : setisDecreasing(true);
                  const headers = new Headers();
                  headers.append("Content-Type", "application/json");
                  headers.append("Authorization", `Bearer ${token}`);
                  const response = await fetch(
                    isIn
                      ? apiRoutes.client.increaseCart
                      : isDel
                        ? apiRoutes.client.removeCart
                        : apiRoutes.client.decreaseCart,
                    {
                      method: "POST",
                      body: JSON.stringify({ cart: cartId }),
                      headers: headers,
                    },
                  );
                  const result = await response.json();
                  if (result?.status) {
                    cartStore.setCartList(result?.data);
                    setIsRealod(!isRealod);
                  }
                } catch (error) {
                  console.log(error);
                } finally {
                  setisIncreasing(false);
                  setisDecreasing(false);
                  setisRemoving(false);
                }
              };

              if (!hasVariantsOrMar) {
                return (
                  <div>
                    {item?.cartDetails?.map((cartItem: any, index: number) => (
                      <div
                        key={index}
                        className="flex items-center justify-between mt-3"
                      >
                        <button
                          onClick={() => increaseCart(cartItem.id, false, true)}
                          className="flex items-center gap-1 text-gray-400 text-sm hover:text-red-500"
                        >
                          {isRemoving ? (
                            <Icons.loading />
                          ) : (
                            <Trash2 size={20} />
                          )}
                          Remove
                        </button>

                        <div className="flex items-center gap-2">
                          <button
                            onClick={() =>
                              increaseCart(cartItem.id, false, false)
                            }
                            className="w-8 h-8 rounded bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200"
                          >
                            {isDecreasing ? (
                              <Icons.loading />
                            ) : (
                              <Minus size={16} />
                            )}
                          </button>
                          <span className="w-8 text-center font-medium">
                            {cartItem?.quantity}
                          </span>
                          <button
                            onClick={() =>
                              increaseCart(cartItem.id, true, false)
                            }
                            className="w-8 h-8 rounded bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200"
                          >
                            {isIncreasing ? (
                              <Icons.loading />
                            ) : (
                              <Plus size={16} />
                            )}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                );
              } else {
                return (
                  <div className="mt-4 space-y-2">
                    {item?.cartDetails?.map((cartItem: any, index: number) => (
                      <div
                        key={index}
                        className="flex items-center justify-between bg-orange-50 p-2 rounded"
                      >
                        <button
                          onClick={() => increaseCart(cartItem.id, false, true)}
                          className="flex items-center gap-2 hover:text-red-500"
                        >
                          {isRemoving ? (
                            <Icons.loading />
                          ) : (
                            <Trash2 size={20} className="text-orange-400" />
                          )}
                          <div className="text-xs text-left">
                            {cartItem?.marinationType?.title && (
                              <div>
                                {cartItem.marinationType.title}
                                {cartItem.marinationType.value && (
                                  <span className="text-green-600 ml-1">
                                    (+₹{cartItem.marinationType.value})
                                  </span>
                                )}
                              </div>
                            )}
                            {cartItem?.variant?.title && (
                              <div>
                                {cartItem.variant.title}
                                {cartItem.variant.value && (
                                  <span className="text-green-600 ml-1">
                                    (+₹{cartItem.variant.value})
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </button>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() =>
                              increaseCart(cartItem.id, false, false)
                            }
                            className="w-6 h-6 rounded bg-orange-200 flex items-center justify-center text-orange-600"
                          >
                            {isDecreasing ? (
                              <Icons.loading />
                            ) : (
                              <Minus size={12} />
                            )}
                          </button>
                          <span className="w-6 text-center text-sm">
                            {cartItem.quantity}
                          </span>
                          <button
                            onClick={() =>
                              increaseCart(cartItem.id, true, false)
                            }
                            className="w-6 h-6 rounded bg-orange-200 flex items-center justify-center text-orange-600"
                          >
                            {isIncreasing ? (
                              <Icons.loading />
                            ) : (
                              <Plus size={12} />
                            )}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                );
              }
            })()}
          </div>

          <div className="bg-white rounded-lg px-2 py-4 shadow-sm">
            <div className="flex justify-between text-sm text-[#303030] mb-2">
              <span>No.of items</span>
              <span>{item?.cartDetails?.length}</span>
            </div>
            <div className="flex justify-between text-sm text-[#303030]">
              <span>Total weight</span>
              <span>
                {convertToGrams(item?.weight, item?.cartDetails?.length || 1)}g
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
