interface PaymentMethodProps {
  paymentMethod: string;
  choosePayment: (data: string) => void;
}

export default function PaymentMethod({
  paymentMethod,
  choosePayment,
}: PaymentMethodProps) {
  const paymentMethods = [
    {
      id: "upi",
      name: "Online Payment (HDFC Gateway)",
      description:
        "Pay securely using HDFC payment gateway - UPI, Cards, Net Banking",
    },
    {
      id: "cod",
      name: "Cash on Delivery",
      description: "Pay cash when your order is delivered",
    },
  ];

  return (
    <div className="col-span-7 bg-white shadow-md rounded-xl overflow-y-auto max-h-full">
      <div className="p-6">
        <h1 className="text-2xl font-semibold text-gray-800 mb-8">Payment</h1>

        <h2 className="text-lg font-medium text-gray-800 mb-6">Payment</h2>

        <div className="space-y-4 mb-8">
          {paymentMethods.map((method) => (
            <div
              key={method.id}
              className="border rounded-lg p-4 hover:border-blue-300 transition-colors"
            >
              <div className="flex items-start">
                <input
                  name="payment"
                  value={method.id}
                  type="radio"
                  checked={paymentMethod === method.id}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded mt-1"
                  onChange={() => choosePayment(method.id)}
                />
                <div className="ml-3">
                  <label className="text-lg font-medium text-gray-800 cursor-pointer">
                    {method.name}
                  </label>
                  <p className="text-sm text-gray-600 mt-1">
                    {method.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Online Payment */}
        {paymentMethod === "upi" && (
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="font-semibold text-gray-800 mb-3">
              Secure Online Payment
            </h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>• Pay securely through HDFC payment gateway</p>
              <p>• Supports UPI, Credit/Debit Cards, Net Banking</p>
              <p>• 256-bit SSL encryption for secure transactions</p>
              <p>• Instant payment confirmation</p>
            </div>
          </div>
        )}

        {/* Cash on Delivery */}
        {paymentMethod === "cod" && (
          <div className="bg-green-50 rounded-lg p-6">
            <h3 className="font-semibold text-gray-800 mb-3">
              Cash on Delivery
            </h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>• Pay cash when your order is delivered</p>
              <p>• No advance payment required</p>
              <p>• Please keep exact change ready</p>
              <p>• Available for orders within delivery area</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
