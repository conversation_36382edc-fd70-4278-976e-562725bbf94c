import { useFetch } from "@/utils/hooks/useFeatch";
import { useEffect, useState } from "react";
import apiRoutes from "@/utils/api/routes";
import { toast } from "react-toastify";
import CreateAddress from "./createAddress";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";

interface AddressProps {
  token: string | undefined;
  address: string;
  setAddress: React.Dispatch<React.SetStateAction<string>>;
}

export default function Address({ token, address, setAddress }: AddressProps) {
  const [isRealod, setisRealod] = useState(false);
  const [isAddressModalOpen, setIsAddressModalOpen] = useState(false);
  const [isCreateAddressOpen, setIsCreateAddressOpen] = useState(false);

  const openAddressModal = () => setIsAddressModalOpen(true);
  const closeAddressModal = () => setIsAddressModalOpen(false);
  const openCreateAddressModal = () => {
    setIsCreateAddressOpen(true);
    setIsAddressModalOpen(false); // Close the address selection modal
  };
  const closeCreateAddressModal = () => setIsCreateAddressOpen(false);

  const { data: alladress } = useFetch<any>({
    url: apiRoutes.client.getAllAddress,
    token: token,
    start: !!token,
    dep: isRealod,
  });

  const { data: mainAddress } = useFetch<any>({
    url: apiRoutes.client.geActiveAddress,
    token: token,
    start: !!token,
    dep: isRealod,
  });

  useEffect(() => {
    setAddress(mainAddress?.id);
  }, [mainAddress]);

  const handleAddressSelect = async () => {
    try {
      let res = await fetch(apiRoutes.client.chooseAddress, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ addressId: address }),
      });
      const result = await res.json();
      if (result?.status) {
        setisRealod(!isRealod);
        toast.success("Address changed successfully!");
        closeAddressModal();
      }
    } catch (err) {
      console.error("Error selecting address:", err);
    }
  };

  const updateAddressList = () => {
    setisRealod(!isRealod);
    closeCreateAddressModal();
  };

  return (
    <>
      <div className="mt-4 bg-white rounded-md border border-[#E2E2E2] p-4">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm text-gray-500">Deliver to:</p>
            <p className="text-base font-semibold text-black">
              {mainAddress?.name}
            </p>
            <p className="text-sm text-gray-500">{mainAddress?.address}</p>
          </div>
          <button
            className="text-[#3B66B1] text-sm font-medium"
            onClick={openAddressModal}
          >
            Change
          </button>
        </div>
      </div>

      {isAddressModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white w-[90%] max-w-md rounded-lg p-6 relative max-h-[80vh] overflow-y-auto">
            <button
              onClick={closeAddressModal}
              className="absolute top-3 right-3 text-gray-500"
            >
              ✕
            </button>

            <h2 className="text-lg font-semibold mb-4">
              Change Delivery Location
            </h2>

            {alladress?.data.map((item: any, i: number) => (
              <label
                key={i}
                className={`block border p-4 rounded-md mb-3 cursor-pointer ${
                  address === item.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-300"
                }`}
              >
                <input
                  type="radio"
                  name="address"
                  value={item.id}
                  checked={address === item.id}
                  onChange={() => setAddress(item.id)}
                  className="mr-3 accent-blue-600"
                />
                <div>
                  <p className="font-semibold">{item.name}</p>
                  <p className="text-sm text-gray-600">{item.address}</p>
                  <p className="text-sm text-gray-600">{item.phone}</p>
                </div>
              </label>
            ))}

            <button
              onClick={openCreateAddressModal}
              className="w-full mt-2 bg-blue-100 text-blue-700 py-2 rounded-md font-medium"
            >
              + Delivery to new address
            </button>

            <button
              onClick={handleAddressSelect}
              className="w-full mt-4 bg-[#3B66B1] text-white py-2 rounded-md font-semibold"
            >
              Select
            </button>
          </div>
        </div>
      )}

      {/* Create Address Modal */}
      <Sheet open={isCreateAddressOpen} onOpenChange={closeCreateAddressModal}>
        <SheetContent className="bg-white dark:bg-white w-96 overflow-y-auto">
          <SheetHeader className="bg-white">
            <SheetTitle className="text-dark-50 dark:text-dark-50">
              Add New Address
            </SheetTitle>
          </SheetHeader>
          <div>
            <CreateAddress update={updateAddressList} />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
