import { useFetch } from "@/utils/hooks/useFeatch";
import { useState } from "react";
import apiRoutes from "@/utils/api/routes";
import { toast } from "react-toastify";

interface AddressProps {
  token: string | undefined;
  timeslot: string;
  setTimeslot: React.Dispatch<React.SetStateAction<string>>;
}

export default function Timeslot({
  token,
  timeslot,
  setTimeslot,
}: AddressProps) {
  const [showModal, setShowModal] = useState(false);

  const { data: slotsData } = useFetch<any>({
    url: apiRoutes.client.getslots,
    token: token,
    start: !!token,
  });

  const { data, isLoading } = useFetch<any>({
    url: apiRoutes.client.getHighlite,
    start: true,
  });

  return (
    <>
      <div className="mt-3 mb-10 bg-white rounded-md border border-[#E2E2E2] p-4">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-gray-500">
              Your Order will be delivered in between
            </p>
            <p className="text-base font-semibold text-black">{timeslot}</p>
          </div>
          <button
            className="text-[#3B66B1] text-sm font-medium"
            onClick={() => setShowModal(true)}
          >
            Change
          </button>
        </div>
      </div>

      {showModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
          <div className="bg-white w-[90%] max-w-md rounded-xl p-6 relative">
            <button
              onClick={() => setShowModal(false)}
              className="absolute top-4 right-4 text-gray-500 text-xl"
            >
              ✕
            </button>

            <h2 className="text-xl font-semibold mb-4">
              Change Delivery Time Slot
            </h2>

            <div className="bg-blue-100 text-blue-800 text-sm p-3 rounded-md mb-4">
              {isLoading
                ? "Orders placed after 10:00 PM will be delivered the next day. Delivery times are from 6:00 AM to 10:00 PM."
                : data}
            </div>
            <div className="max-h-[40vh] overflow-y-auto pr-1">
              {slotsData?.map((slot: any, index: number) => (
                <label
                  key={index}
                  className={`flex items-center mb-3 p-3 rounded-md border cursor-pointer ${
                    timeslot === slot
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-300"
                  }`}
                >
                  <input
                    type="radio"
                    name="delivery-slot"
                    value={slot}
                    checked={timeslot === slot}
                    onChange={() => setTimeslot(slot)}
                    className="mr-3 accent-blue-600"
                  />
                  {slot}
                </label>
              ))}
            </div>

            <button
              onClick={() => {
                setShowModal(false);
                toast.success("Time slot changed successfully!");
              }}
              className="w-full mt-4 bg-[#3B66B1] text-white py-2 rounded-lg font-semibold"
            >
              Change
            </button>
          </div>
        </div>
      )}
    </>
  );
}
