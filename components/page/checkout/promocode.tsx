import { Icons } from "@/components/presentation/icons";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { useFetch } from "@/utils/hooks/useFeatch";
import { useState, useEffect } from "react";
import apiRoutes from "@/utils/api/routes";
import { toast } from "react-toastify";

interface PromocodeProps {
  token: string | undefined;
  promoCode: string;
  setPromoCode: React.Dispatch<React.SetStateAction<string>>;
  setDiscount: React.Dispatch<React.SetStateAction<number>>;
}

export default function Promocode({
  token,
  promoCode,
  setPromoCode,
  setDiscount,
}: PromocodeProps) {
  const [isOpen, setisOpen] = useState(false);
  const [inputPromoCode, setInputPromoCode] = useState(promoCode || "");
  const [isApplying, setIsApplying] = useState(false);
  const [error, setError] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  const { data: promoCodeList } = useFetch<any>({
    url: apiRoutes.client.promoCodeList,
    token: token,
    start: !!token,
  });

  // Sync input field with promoCode prop
  useEffect(() => {
    setInputPromoCode(promoCode || "");
  }, [promoCode]);

  // Filter promo codes based on search query
  const filteredPromoCodeList =
    promoCodeList?.filter((item: any) => {
      if (!searchQuery) return true;

      const query = searchQuery.toLowerCase();
      return (
        item?.code?.toLowerCase().includes(query) ||
        item?.condition?.toLowerCase().includes(query) ||
        item?.type?.toLowerCase().includes(query)
      );
    }) || [];

  const applyPromoCode = async (code: string) => {
    if (!code.trim()) {
      setError("Please enter a promo code");
      return;
    }

    try {
      setIsApplying(true);
      setError("");

      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      if (token) {
        headers.append("Authorization", `Bearer ${token}`);
      }

      const response = await fetch(apiRoutes.client.promoCodeApply, {
        method: "POST",
        headers: headers,
        body: JSON.stringify({ code: code }),
      });

      const result = await response.json();

      if (result?.code === 200) {
        setDiscount(result?.data || 0);
        setPromoCode(code);
        setInputPromoCode(code);
        setError("");
        setisOpen(false);
        toast.success("Promo code applied successfully!");
      } else {
        setError(result?.message || "Failed to apply promo code");
      }
    } catch (error) {
      console.log(error, "ss");
      setError("Something went wrong. Please try again.");
    } finally {
      setIsApplying(false);
    }
  };

  const handleInputApply = () => {
    applyPromoCode(inputPromoCode);
  };

  const handleListApply = (code: string) => {
    setisOpen(false);
    applyPromoCode(code);
  };

  const removePromoCode = () => {
    setPromoCode("");
    setInputPromoCode("");
    setError("");
    setDiscount(0);
    toast.success("Promo code removed successfully!");
  };

  return (
    <>
      <div>
        <div className="relative">
          <input
            type="text"
            id="search"
            value={inputPromoCode}
            onChange={(e) => setInputPromoCode(e.target.value)}
            className={`block w-full p-4 ps-4 text-sm text-gray-900 rounded-lg bg-[#F4F4F5] ${promoCode ? "pr-32" : "pr-20"}`}
            placeholder="Enter Promo Code"
            readOnly={!!promoCode}
          />
          {promoCode ? (
            <div className="absolute end-2.5 bottom-2.5 flex gap-2">
              <button
                type="button"
                onClick={removePromoCode}
                className="text-white bg-red-500 hover:bg-red-600 focus:outline-none font-medium rounded-lg text-sm px-4 py-2"
              >
                Remove
              </button>
            </div>
          ) : (
            <button
              type="button"
              onClick={handleInputApply}
              disabled={isApplying}
              className="text-white absolute end-2.5 bottom-2.5 bg-[#00A6A6] focus:outline-none font-medium rounded-lg text-sm px-7 py-2 disabled:opacity-50"
            >
              {isApplying ? <Icons.loading className="w-4 h-4" /> : "Apply"}
            </button>
          )}
        </div>
        {error && <div className="mt-2 text-red-500 text-sm">{error}</div>}
        {promoCode && (
          <div className="mt-2 text-green-600 text-sm">
            ✓ Promo code "{promoCode}" applied successfully!
          </div>
        )}
      </div>

      <div
        onClick={() => setisOpen(true)}
        className="cursor-pointer flex justify-between mt-4 bg-[#EDF7F2] w-full rounded-md"
      >
        <button className="text-sm font-normal text-[#00A6A6] py-1.5 flex gap-2 px-4">
          <Icons.offerIcon /> View coupons and offers available
        </button>
        <div className="flex items-center px-4">
          <Icons.nextArrow />
        </div>
      </div>

      <Dialog open={isOpen} onOpenChange={() => setisOpen(false)}>
        <DialogContent className="w-1/2">
          <DialogHeader>
            <DialogTitle className=" flex justify-center text-[1.4rem] font-semibold text-[#1C1C1C]">
              Apply Coupon
            </DialogTitle>
            <div className="mt-8">
              <div className="flex items-center">
                <label htmlFor="voice-search" className="sr-only">
                  Search
                </label>
                <div className="relative w-full">
                  <input
                    type="text"
                    id="voice-search"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-[#F0EFEF] placeholder:text-base placeholder:font-normal placeholder:text-[#8C8C8C] text-gray-900 text-sm rounded-lg  block w-full ps-2 p-2.5 "
                    placeholder="Search Coupon"
                  />
                  {searchQuery ? (
                    <button
                      type="button"
                      onClick={() => setSearchQuery("")}
                      className="absolute inset-y-0 end-0 flex items-center pe-3 text-gray-400 hover:text-gray-600"
                    >
                      ✕
                    </button>
                  ) : (
                    <button
                      type="button"
                      className="absolute inset-y-0 end-0 flex items-center pe-3"
                    >
                      <Icons.search />
                    </button>
                  )}
                </div>
              </div>

              <div className="mt-8">
                <h5 className="text-sm font-semibold text-dark-500 mt-2">
                  {`Available Coupons (${filteredPromoCodeList?.length || 0})`}
                </h5>
                <div className="mt-2">
                  {filteredPromoCodeList?.length > 0 ? (
                    filteredPromoCodeList.map((item: any, index: number) => (
                      <div
                        key={index}
                        className=" px-3 py-2 border border-[#CECECE] rounded-lg mt-1"
                      >
                        <div className="flex justify-between gap-4 items-center">
                          <div className="flex gap-4">
                            <input
                              id="default-checkbox"
                              type="radio"
                              name="promocode"
                              className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 "
                            />
                            <div>
                              <label
                                htmlFor="default-checkbox"
                                className="text-sm font-medium text-gray-900 dark:text-gray-300"
                              >
                                <span className="bg-[#DEFFDC] border border-[#1AAF6F] border-dotted text-dark-500 text-xs font-medium me-2 px-2.5 py-0.5 rounded">
                                  {item?.code}
                                </span>
                              </label>
                              <p className="mt-1 text-xs font-medium text-[#1AAF6F]">
                                {`${item?.discount} ${item?.type === "percentage" ? "%" : "₹"} discount on orders above ${item?.minPurchase} upto ₹ ${item?.maxDiscount}`}
                              </p>
                            </div>
                          </div>
                          <button
                            className="text-sm font-medium text-[#E03B3B] disabled:opacity-50"
                            onClick={() => handleListApply(item?.code)}
                            disabled={isApplying}
                          >
                            {isApplying ? (
                              <Icons.loading className="w-4 h-4" />
                            ) : (
                              "Apply"
                            )}
                          </button>
                        </div>
                        <div className="ms-10">
                          <hr className="h-[1px] w-full bg-[#CECECE] my-2" />
                          <p className="text-[0.7rem] font-normal text-dark-500">
                            {item?.condition}
                          </p>
                          <button className="text-[0.7rem] font-medium text-dark-500">
                            View Details
                          </button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <p>No coupons found matching your search.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
}
