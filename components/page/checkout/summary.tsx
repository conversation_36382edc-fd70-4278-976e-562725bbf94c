import { Icons } from "@/components/presentation/icons";
import { useCartStore } from "@/utils/store/cart";

interface SummaryProps {
  total: number;
  discount: number;
  platformFee: number;
  deliveryCharge: number;
}

export default function Summary({
  total,
  discount,
  platformFee,
  deliveryCharge,
}: SummaryProps) {
  const cartStore = useCartStore();
  const itemCount = cartStore.getCartCount() || 0;

  return (
    <div className="py-1 mt-10">
      <h2 className="text-2xl font-semibold text-dark-500">
        Price Summary{" "}
        <span className="text-base font-normal text-dark-450">
          ({itemCount} Items)
        </span>{" "}
      </h2>

      <div className="mt-5">
        <div className="flex justify-between mb-2">
          <p className="text-sm font-normal text-[#303030]">Item Subtotal</p>
          <p className="text-sm font-normal text-[#303030]">₹ {total}</p>
        </div>
        <div className="flex justify-between mb-2">
          <p className="text-sm font-normal text-[#303030]">platform Fee</p>
          <p className="text-sm font-normal text-[#303030]">₹ {platformFee}</p>
        </div>
        <div className="flex justify-between mb-2">
          <p className="text-sm font-normal text-[#303030]">Delivery Charge</p>
          <p className="text-sm font-normal text-[#303030]">
            {deliveryCharge === 0 ? "Free" : `₹ ${deliveryCharge}`}
          </p>
        </div>
        {discount > 0 && (
          <div className="flex justify-between mb-3">
            <p className="text-sm font-normal text-[#303030]">
              Coupon / Promo code
            </p>
            <p className="text-sm font-normal text-[#00A645]">-{discount}</p>
          </div>
        )}
        <div className="border border-t-[#E2E2E2] my-3 border-dotted" />

        <div className="flex justify-between mb-3">
          <p className="text-base font-semibold text-[#303030]">
            Amount Payable
          </p>
          <p className="text-base font-semibold text-[#303030]">
            ₹ {Number(total) + Number(platformFee) + Number(deliveryCharge) - Number(discount)}
          </p>
        </div>
        <hr />
      </div>
    </div>
  );
}
