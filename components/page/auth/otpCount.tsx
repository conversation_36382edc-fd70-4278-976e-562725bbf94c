import React, { useEffect, useState } from "react";

interface Props {
  resend: () => void;
}

function OtpCountdown({ resend }: Props) {
  const [time, setTime] = useState(60);
  const [isResent, setisResent] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setTime((prevTime) => {
        if (prevTime === 0) {
          clearInterval(timer);
          return 0;
          // Optionally, add logic to handle what happens when the countdown reaches zero
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isResent]);

  return (
    <>
      {time != 0 && <span className="text-xs">0:{time} </span>}
      {time == 0 && (
        <button
          onClick={() => {
            resend();
            setTime(60);
            setisResent(!isResent);
          }}
          className="text-[#0071BC] text-[0.6rem]"
        >
          Request again{" "}
        </button>
      )}
    </>
  );
}

export default OtpCountdown;
