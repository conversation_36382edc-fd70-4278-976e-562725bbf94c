import { Icons } from "@/components/presentation/icons";
import React, { useState } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import routes from "@/utils/api/routes";

type Inputs = {
  phone: string;
};

function MobileLogin({ handleOtp }: { handleOtp: (data: any) => any }) {
  const [isLoading, setisLoading] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Inputs>();
  const onSubmit: SubmitHandler<Inputs> = (data) => requestOtp(data);

  const requestOtp = async (data: Inputs) => {
    try {
      setisLoading(true);
      const res = await fetch(routes.client.requestOtp, {
        method: "POST",
        body: JSON.stringify(data),
        headers: { "Content-Type": "application/json" },
      });
      const result = await res.json();
      if (result?.status && result.data?.accessToken) {
        localStorage.setItem("accessToken", result.data.accessToken);

        handleOtp(data);
      } else {
        alert(result?.message || "OTP verification failed");
      }
      console.log(result);
    } catch (error) {
      console.log(error);
    } finally {
      setisLoading(false);
    }
  };

  return (
    <div>
      <h4 className="mt-3 text-xs font-medium text-dark-500">
        Login via Mobile OTP
      </h4>
      <p className="text-dark-450 text-[0.63rem] font-normal">
        Enter your 10 digit mobile number, we send a 6 digit OTP to that number
      </p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mt-4">
          <label className="text-xs font-normal text-dark-500 mb-2">
            Mobile Number
          </label>

          <div className="relative">
            <input
              type="text"
              {...register("phone", {
                required: "Phone is required",
                pattern: {
                  value: /^\d{10}$/,
                  message: "Phone number in invalid",
                },
              })}
              className="py-3 px-4 ps-20 block w-full text-[0.64rem] font-normal text-[#8C8C8C] border-gray-200 shadow-sm  bg-[#E8E8E8] text-sm focus:z-10  disabled:opacity-50 disabled:pointer-events-none outline-none"
              placeholder="Enter your mobile number"
            />
            <div className="absolute inset-y-0 start-0 flex items-center text-gray-500 ps-px">
              <label className="sr-only">Country</label>
              <select
                id="hs-inline-leading-select-country"
                name="hs-inline-leading-select-country"
                className="block w-full bg-black border-black py-2.5 text-white px-3"
              >
                <option value="+91" defaultChecked>
                  +91
                </option>
              </select>
            </div>
          </div>
          <span className="text-red-500 text-xs h-1">
            {errors?.phone?.message}
          </span>
        </div>

        <div className="mt-14">
          <button
            disabled={isLoading}
            type="submit"
            className="py-3 px-4 inline-flex items-center justify-center gap-x-2 text-sm font-semibold w-full bg-[#FBB03B] text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-none "
          >
            Request OTP {isLoading ? <Icons.loading /> : <Icons.arrow />}
          </button>
        </div>
      </form>
    </div>
  );
}

export default MobileLogin;
