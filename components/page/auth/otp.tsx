"use client";

import { Icons } from "@/components/presentation/icons";
import { signIn } from "next-auth/react";
import React, { useState } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import apiRoutes from "@/utils/api/routes";
import OtpCountdown from "./otpCount";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

type Inputs = {
  otp1: string;
  otp2: string;
  otp3: string;
  otp4: string;
  otp5: string;
  otp6: string;
};

function Otp({ phone, close }: { phone: string; close: () => void }) {
  const router = useRouter();
  const [isWrong, setisWrong] = useState(false);
  const [otpDigit, setotpDigit] = useState(["", "", "", "", "", ""]);
  const [isLogin, setisLogin] = useState(false);
  const [isLoading, setisLoading] = useState(false);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<any>();

  const onSubmit: SubmitHandler<Inputs> = async (data) => {
    // setisLogin(true);
    // let otpList = Object.values(data);
    // const otp = Number(otpList.join(""));

    // const response = await signIn("credentials", {
    //   username: phone,
    //   password: otp,
    //   redirect: false,
    //   callbackUrl: "/",
    // });

    // if (response?.error) {
    //   setisLogin(false);
    //   setisWrong(true);
    //   return;
    // }
    // setisLogin(false);
    // close();
    try {
      setisLoading(true);
      const res = await fetch(apiRoutes.client.verifyOtp, {
        method: "POST",
        body: JSON.stringify({ phone, otp: otpDigit.join("") }),
        headers: { "Content-Type": "application/json" },
      });
      const result = await res.json();
      if (result?.status == "SUCCESS") {
        toast({ description: "Login successfull" });
        router.push("/");
        close();
        // handleOtp(data)
      }
    } catch (error) {
      console.log(error);
      toast({ description: "Failed login" });
    } finally {
      setisLoading(false);
    }
  };

  const onChange = (event: any, index: number) => {
    const isNotNumber = isNaN(event.target.value);
    if (!isNotNumber) {
      const tempDigit = [...otpDigit];
      tempDigit[index] = event.target.value;
      setotpDigit(tempDigit);
    }
  };

  const requestOtp = async () => {
    try {
      setisLoading(true);
      const res = await fetch(apiRoutes.client.requestOtp, {
        method: "POST",
        body: JSON.stringify({ phone }),
        headers: { "Content-Type": "application/json" },
      });
      const result = await res.json();
      if (result?.status) {
        toast({ description: "Otp sent" });
        // handleOtp(data)
      }
    } catch (error) {
      console.log(error);
      toast({ description: "Failed send otp" });
    } finally {
      setisLoading(false);
    }
  };

  const reSend = () => {
    setotpDigit(["", "", "", "", "", ""]);
    setisWrong(false);
    requestOtp();
  };

  return (
    <div>
      <h4 className="mt-3 text-sm font-medium text-dark-500">
        Verify with OTP
      </h4>
      <p className="text-dark-450 text-[0.63rem] font-normal">
        The OTP will sent to {phone}
      </p>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mt-4">
          {!isWrong ? (
            <label className="text-xs font-normal text-dark-500 mb-2">
              Enter 6 digit OTP
            </label>
          ) : (
            <label className="text-xs font-normal text-[#E03B3B] mb-2">
              Wrong OTP entered, Check OTP
            </label>
          )}

          <div>
            <div className="flex space-x-3 mt-0.5">
              {otpDigit?.map((item, index) => (
                <input
                  key={index}
                  {...register(`otp${index + 1}`, {
                    required: true,
                    maxLength: 1,
                    onChange: (events: any) => onChange(events, index),
                  })}
                  type="text"
                  value={item}
                  inputMode="numeric"
                  maxLength={1}
                  className={`block h-[38px] w-[38px]  text-center bg-[#E8E8E8] ${
                    isWrong
                      ? "border-[#E03B3B] border text-[#E03B3B]"
                      : "border-transparent"
                  }  text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none`}
                />
              ))}
            </div>
            <div className="flex justify-center items-center mt-4">
              <p className="text-[0.7rem] font-normal text-dark-500">
                OTP not Received? <OtpCountdown resend={reSend} />
              </p>
            </div>
          </div>
        </div>

        <div className="mt-5">
          <button
            disabled={isLogin}
            type="submit"
            className="py-3 px-4 inline-flex items-center justify-center gap-x-2 text-sm font-semibold w-full bg-[#FBB03B] text-white disabled:bg-slate-500 disabled:opacity-50 disabled:pointer-events-none focus:outline-none "
          >
            Login {isLogin ? <Icons.loading /> : <Icons.arrow />}
          </button>
        </div>
      </form>
    </div>
  );
}

export default Otp;
