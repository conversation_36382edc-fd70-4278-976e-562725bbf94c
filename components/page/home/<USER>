"use client";

import ProductCard from "@/components/main/productCard";
import Title from "@/components/main/title";
import React from "react";
import { Product } from "@/utils/types/product";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import { useFetch } from "@/utils/hooks/useFeatch";
import ProductCardPlaceHolder from "@/components/presentation/productCardPlaceHolder";

interface Props {
  title: string;
  url: string;
  subTitle?: string;
  timer?: string;
  page?: string;
}

var sliderSettings = {
  dots: false,
  infinite: false,
  arrow: true,
  slidesToShow: 4.7,
  slidesToScroll: 1,
  speed: 1000,
};

function FlashSales({ title, url, subTitle, timer, page }: Props) {
  const { data, isLoading } = useFetch<any>({ url, start: true });

  return (
    <div className="py-10">
      {data && data?.data?.length > 0 && (
        <div>
          <Title
            title={title}
            subtitle={subTitle}
            page={page}
            timer={data?.flashSale?.endDate}
          />

          {isLoading ? (
            <div className="mt-7">
              <div className="grid grid-cols-4 gap-1">
                {[1, 2, 3, 4].map((item) => (
                  <div key={item}>
                    {" "}
                    <ProductCardPlaceHolder />{" "}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="mt-7">
              {!!data?.data?.length && (
                <Slider {...sliderSettings} className="px-4 caros">
                  {data?.data?.map((item: Product, index: number) => (
                    <div key={index} className="pe-2">
                      {" "}
                      <ProductCard data={item} />
                    </div>
                  ))}
                </Slider>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default FlashSales;
