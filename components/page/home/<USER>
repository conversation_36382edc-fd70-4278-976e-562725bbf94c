"use client";
import React from "react";
import { Banner } from "@/utils/types/common";
import { useFetch } from "@/utils/hooks/useFeatch";
import apiRoutes from "@/utils/api/routes";

function Highlite() {
  const { data, isLoading } = useFetch<any>({
    url: apiRoutes.client.getHighlite,
    start: true,
  });

  return (
    <div className="mt-10">
      <div className="flex justify-center gap-2">
        <div className="flex gap-2 items-center bg-[#CFE2F2] p-1 rounded-lg">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="21"
            height="19"
            viewBox="0 0 21 19"
            fill="none"
          >
            <path
              d="M10.2243 0C4.57336 0 0 4.24888 0 9.5C0 14.7506 4.57284 19 10.2243 19C15.8753 19 20.4487 14.7511 20.4487 9.5C20.4487 4.24943 15.8758 0 10.2243 0ZM11.2743 13.2711C11.2743 13.5712 10.8033 13.8713 10.2245 13.8713C9.61891 13.8713 9.18829 13.5712 9.18829 13.2711V8.50673C9.18829 8.1566 9.61895 7.91896 10.2245 7.91896C10.8033 7.91896 11.2743 8.1566 11.2743 8.50673V13.2711ZM10.2246 6.7686C9.60549 6.7686 9.12103 6.34344 9.12103 5.86822C9.12103 5.39303 9.60553 4.98037 10.2246 4.98037C10.8302 4.98037 11.3148 5.39303 11.3148 5.86822C11.3148 6.34344 10.8302 6.7686 10.2246 6.7686Z"
              fill="#3559A8"
            />
          </svg>
          <p className="text-base text-[#3559A8]">
            {isLoading
              ? "Orders placed after 12:00 PM will be delivered the next day. Delivery times are from 6:00 AM to 12:00 PM."
              : data}
          </p>
        </div>
      </div>
    </div>
  );
}

export default Highlite;
