"use client";

import React, { useState } from "react";
import Image from "next/image";
import "react-modal-video/css/modal-video.css";
import ModalVideo from "react-modal-video";

function Video() {
  const [isOpen, setOpen] = useState(false);

  return (
    <div>
      <Image
        onClick={() => setOpen(!isOpen)}
        src="/images/offer/video-banner.png"
        width={950}
        height={225}
        alt="offer"
        className="object-cover aspect-auto w-full h-80 rounded-xl"
      />
      <ModalVideo
        channel="youtube"
        youtube={{ mute: 0, autoplay: 0 }}
        isOpen={isOpen}
        videoId="JIbIYCM48to"
        onClose={() => setOpen(false)}
      />
    </div>
  );
}

export default Video;
