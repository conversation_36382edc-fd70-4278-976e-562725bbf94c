import React from "react";
import Image from "next/image";
import apiUrl from "@/utils/api/routes";
import Link from "next/link";

async function getData() {
  const res = await fetch(apiUrl.server.getCategories, {
    method: "post",
    cache: "no-cache",
  });
  const result = await res.json();
  if (!res.ok || !result?.status) {
    throw new Error("Failed to fetch data");
  }
  const { data } = result;
  return data;
}

async function Page() {
  const { data = [] }: { data: any } = await getData();

  return (
    <div className="bg-white rounded-2xl  w-full p-5 shadow-lg">
      <h3 className="text-dark-50 font-semibold text-3xl">
        Shop by Categories
      </h3>
      <div className="bg-[#3359A8] rounded-2xl p-3 mt-10">
        <div className="grid grid-cols-5 gap-3">
          {data?.map((item: any, index: number) => (
            <div key={index} className="p-3 mx-auto glass-card">
              <Link href={`/category/${item?.id}`}>
                <Image
                  src={item?.logo}
                  width={151}
                  height={151}
                  alt="category"
                  className="aspect-square object-contain mx-auto rounded-full"
                />
                <h5 className="text-center w-100 text-white text-base font-medium mt-4">
                  {item.title}
                </h5>
                <span className=" text-center font-light text-white text-xs line-clamp-2">
                  {item?.desctiption}
                </span>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default Page;
