"use client";

import { useState } from "react";
import { useF<PERSON>, SubmitHandler } from "react-hook-form";
import apiUrl from "@/utils/api/routes";
import { useToast } from "@/components/ui/use-toast";
import { ToastAction } from "@/components/ui/toast";

interface Inputs {
  name: string;
  phone?: string;
  email: string;
  message: string;
}

interface Props {
  isShowTitle?: boolean;
}

function Enquiry({ isShowTitle }: Props) {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Inputs>();
  const [isLoading, setisLoading] = useState(false);
  const { toast } = useToast();

  const onSubmit: SubmitHandler<Inputs> = async (data) => {
    try {
      setisLoading(true);
      const response = await fetch(apiUrl.client.submitEnquiry, {
        method: "post",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      const result = await response.json();
      if (result?.status) {
        toast({
          title: "Done",
          description: "Enquiry Submited Successfully",
        });
        reset();
        return null;
      }
      toast({
        variant: "destructive",
        title: "Something Went Wrong",
        description: "Failed to submit enquiry",
        // action: <ToastAction altText="Try again">Try again</ToastAction>,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Something Went Wrong",
        description: "Failed to submit enquiry",
        // action: <ToastAction altText="Try again" onClick={}>Try again</ToastAction>,
      });
      console.log(error);
    } finally {
      setisLoading(false);
    }
  };

  const required = "This field is required";

  return (
    <div className="col-span-5 bg-white p-5 rounded-xl">
      {isShowTitle && (
        <h6 className="text-dark-100 font-semibold text-2xl mb-4">
          We like to hear from you!
        </h6>
      )}
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mt-5">
          <div className="relative z-0">
            <input
              autoComplete="off"
              aria-autocomplete="none"
              type="text"
              {...register("name", {
                required,
                minLength: {
                  value: 4,
                  message: "Minimum 4 character required",
                },
                maxLength: {
                  value: 30,
                  message: "Maximum 30 charcter required",
                },
              })}
              className="block text-[#686868] py-2.5 px-0 w-full text-sm bg-transparent border-0 border-b-2 border-[#C6C6C6] appearance-none   focus:outline-none focus:ring-0 focus:border-[#FBB03B] peer"
              placeholder=""
            />
            <label className="absolute text-sm text-dark-100  duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-red0-5  peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">
              Your Name
            </label>
            {errors?.name && (
              <span className="text-red-50 text-xs">
                {errors?.name?.message}
              </span>
            )}
          </div>
        </div>

        <div className="mt-5">
          <div className="relative z-0 mt-5">
            <input
              autoComplete="off"
              aria-autocomplete="none"
              type="text"
              {...register("phone", {
                pattern: { value: /^\d{10}$/, message: "Invalid Phone number" },
              })}
              className="block py-2.5 px-0 w-full text-sm text-[#686868] bg-transparent border-0 border-b-2 border-[#C6C6C6] appearance-none   focus:outline-none focus:ring-0 focus:border-[#FBB03B] peer"
              placeholder=" "
            />
            <label className="absolute text-sm text-dark-100  duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-[#FBB03B]  peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">
              Mobile Number
            </label>
            {errors?.phone && (
              <span className="text-red-50 text-xs">
                {errors?.phone?.message}
              </span>
            )}
          </div>
        </div>

        <div className="mt-5">
          <div className="relative z-0 mt-5">
            <input
              autoComplete="off"
              aria-autocomplete="none"
              type="text"
              {...register("email", {
                required,
                pattern: {
                  value: /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
                  message: "invalid email",
                },
              })}
              className="block py-2.5 px-0 w-full text-sm text-[#686868] bg-transparent border-0 border-b-2 border-[#C6C6C6] appearance-none   focus:outline-none focus:ring-0 focus:border-[#FBB03B] peer"
              placeholder=" "
            />
            <label className="absolute text-sm text-dark-100  duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-[#FBB03B]  peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">
              Email Id
            </label>
            {errors?.email && (
              <span className="text-red-50 text-xs">
                {errors?.email?.message}
              </span>
            )}
          </div>
        </div>

        <div className="mt-5">
          <div className="relative z-0 mt-5">
            <input
              autoComplete="off"
              maxLength={150}
              aria-autocomplete="none"
              type="text"
              {...register("message", {
                required,
                minLength: {
                  value: 10,
                  message: "Minimum 10 character required",
                },
                maxLength: { value: 150, message: "Maximum 150 character" },
              })}
              className="block py-2.5 px-0 w-full text-sm text-[#686868] bg-transparent border-0 border-b-2 border-[#C6C6C6] appearance-none   focus:outline-none focus:ring-0 focus:border-[#FBB03B] peer"
              placeholder=" "
            />
            <label className="absolute text-sm text-dark-100  duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:left-0 peer-focus:text-[#FBB03B]  peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-6">
              Message
            </label>
            {errors?.message && (
              <span className="text-red-50 text-xs">
                {errors?.message?.message}
              </span>
            )}
          </div>
        </div>

        <button className="py-3 px-10 bg-primary-100 text-white rounded mt-5 float-right text-sm font-semibold">
          {isLoading ? (
            <>
              <svg
                aria-hidden="true"
                role="status"
                className="inline w-4 h-4 me-3 text-white animate-spin"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                  fill="#E5E7EB"
                />
                <path
                  d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                  fill="currentColor"
                />
              </svg>{" "}
              Sending...
            </>
          ) : (
            <span>SEND</span>
          )}
        </button>
      </form>
    </div>
  );
}

export default Enquiry;
