import React from "react";
import { Icons } from "./icons";
import { useRouter } from "next/navigation";

interface EmptyCart {
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

function EmptyCart({ setIsOpen }: EmptyCart) {
  const router = useRouter();
  return (
    <div className="h-[70vh]">
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <div className="flex justify-center">
            <Icons.cartEmpty />
          </div>
          <h2 className="text-[26px] font-semibold text-[#515251] my-2">
            Your bag t is empty!!
          </h2>
          <p className="text-sm font-normal text-[#878787]">
            Looks like you have not added anything to your bag. <br /> Go ahead
            & explore top categories
          </p>

          <div className="mt-5">
            <button
              className="mt-5 text-lg font-semibold text-white py-1.5 px-20 rounded-xl bg-primary-100"
              onClick={() => {
                setIsOpen(false);
                router.push("/");
              }}
            >
              Shop Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EmptyCart;
