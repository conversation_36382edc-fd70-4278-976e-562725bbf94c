import Image from "next/image";
import Link from "next/link";
import React from "react";

function EmptyWishlist() {
  return (
    <div className="flex flex-col items-center justify-center px-4 py-8">
      {/* Illustration */}
      <div className="mb-8">
        <div className="relative w-80 flex items-center justify-center">
          <Image
            src="/images/other/wishlist.png"
            width={400}
            height={100}
            alt="oops"
          />
        </div>
      </div>

      {/* Text content */}
      <div className="text-center mb-8 max-w-md">
        <h2 className="text-xl font-semibold text-gray-800 mb-3">
          Your Wishlist is empty!
        </h2>
        <p className="text-[#7B7A7A] text-base mb-2">
          seems like you don't have wishes here
        </p>
        <p className="text-[#434343] font-medium">Make a wish!</p>
      </div>

      {/* Call to action button */}
      <Link
        href="/"
        className="bg-primary-100 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
      >
        Start Shopping
      </Link>
    </div>
  );
}

export default EmptyWishlist;
