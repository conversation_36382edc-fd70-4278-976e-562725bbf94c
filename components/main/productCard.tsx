"use client";
import React, { useState, useEffect } from "react";
import { AddToCartParams, Product } from "@/utils/types/product";
import Link from "next/link";
import apiRoutes from "@/utils/api/routes";
import { useRouter } from "next/navigation";
import { useWishlistStore } from "@/utils/store/wishlist";
import Modal from "react-modal";
import { AiOutlineClose } from "react-icons/ai";
import { useCartStore } from "@/utils/store/cart";
import { Icons } from "../presentation/icons";
import QuantityControl from "./quantityControl";
import { getCartItem, hasVariantsOrMarination, toFloat } from "@/utils/helper";
import Variantcard from "./variantCard";
import MarinationCard from "./marinationCard";
import { toast } from "react-toastify";

interface ProductProps {
  data: Product;
  isSearch?: boolean;
}

function ProductCard({ data, isSearch }: ProductProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const togglePopup = () => setIsOpen(!isOpen);
  const cartlistState = useCartStore();
  const wishlistState = useWishlistStore();

  const [token, setToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
  }, []);

  const addToCart = async ({
    productId,
    variantId,
    marinationId,
  }: AddToCartParams) => {
    if (!token) {
      router.push("/login");
    } else {
      try {
        const headers = new Headers();
        headers.append("Content-Type", "application/json");
        headers.append("Authorization", `Bearer ${token}`);

        const body: Record<string, any> = {
          id: productId,
          quantity: 1,
        };

        if (variantId) {
          body.variant = variantId;
        }

        if (marinationId) {
          body.marinationType = marinationId;

          if (selectedVariant) {
            body.variant = selectedVariant;
          }
        }

        const response = await fetch(apiRoutes.client.addToCart, {
          method: "POST",
          body: JSON.stringify(body),
          headers: headers,
        });
        const result = await response.json();
        if (result?.status) {
          toast(
            <>
              <Icons.greenbag className="inline-block mr-2" />
              <span className="px-2">Item added to bag successfully</span>
            </>,
          );
          cartlistState.setCartList(result?.data);
        }
      } catch (error) {
        console.log(error);
        toast.error("Failed to add product card!");
      }
    }
  };

  const updateWIshList = async () => {
    const token = localStorage.getItem("accessToken");

    const id = data?.id || data?._id;
    try {
      try {
        const headers = new Headers();
        headers.append("Content-Type", "application/json");
        headers.append("Authorization", `Bearer ${token}`);
        const response = await fetch(apiRoutes.client.addWishList, {
          method: "POST",
          body: JSON.stringify({ id }),
          headers: headers,
        });
        const result = await response.json();
        if (result?.status) {
          wishlistState.setWishList(result?.data || []);
        }
      } catch (error) {
        console.log(error);
        // setaddingToCart(false)
      } finally {
        // setaddingToCart(false)
      }
    } catch (error) {}
  };

  const [selectedVariant, setSelectedVariant] = useState<string | null>(null);

  useEffect(() => {
    if (
      data?.marinationTypes &&
      data.marinationTypes.length > 0 &&
      data?.variants &&
      data.variants.filter((item: any) => item.status === "Active").length > 0
    ) {
      setSelectedVariant(
        data.variants.filter((item: any) => item.status === "Active")[0]?.id
          ?._id,
      );
    }
  }, [data]);

  // horizontal layout
  if (isSearch) {
    return (
      <div
        className={`w-full bg-white border rounded-lg shadow-xl flex ${
          data?.stock ? "" : "opacity-75"
        }`}
      >
        <div className="relative w-64 flex-shrink-0">
          <Link href={`/product/${data?._id}`}>
            <img
              className="rounded-l-lg p-2 rounded-lg object-cover w-full h-48"
              src={
                data?.images?.[0] || "/images/product/fish_product_image.jpg"
              }
              alt=""
            />
            <div className="absolute inset-0 flex flex-col justify-between">
              <div className="flex items-start justify-between">
                <span className="m-4 z-10">
                  <Icons.veg isVeg={data?.isVeg} />
                </span>
              </div>
            </div>
          </Link>
        </div>

        <div className="flex-1 p-4 flex flex-col justify-between">
          <div>
            <Link
              href={`/product/${data?.id}`}
              className="text-[#878787] text-xs"
            >
              1 Pack
            </Link>
            <Link href={`/product/${data?.id}`}>
              <h5 className="mt-1 font-semibold text-sm text-[#232323]">
                {data?.title} | {data?.weight}
              </h5>
            </Link>

            {data?.stock?.stock !== 0 ? (
              <div className="mt-3">
                <div className="flex gap-3 items-center">
                  {data?.offerPrice ? (
                    <div className="flex items-center gap-2">
                      <span className="text-gray-500 line-through text-sm">
                        ₹{toFloat(data?.mrp)}
                      </span>
                      <span className="text-dark-400 font-semibold text-xl">
                        ₹{toFloat(data?.offerPrice)}
                      </span>
                      <span className="bg-gradient-to-r from-[#44ABC9] to-[#59C4C2] text-green-800 text-xs px-2 py-1 rounded">
                        {data?.mrp && data?.offerPrice
                          ? Math.round(
                              ((data.mrp - data.offerPrice) / data.mrp) * 100,
                            )
                          : 0}
                        % Off
                      </span>
                    </div>
                  ) : (
                    <span className="text-dark-300 font-semibold text-xl">
                      ₹{toFloat(data?.mrp)}
                    </span>
                  )}
                </div>

                <div className="flex mt-5 justify-between items-end gap-2">
                  <span className="bg-[#E2E2E2] py-0.5 inline-flex items-center rounded px-3 text-base font-normal mt-3">
                    <Icons.greenStar />
                    <span className="ms-2">
                      {data?.averageRating} | {data?.totalReviews}
                    </span>
                  </span>
                  {data?.stock?.stock ? (
                    <div>
                      {(() => {
                        const cartItem = getCartItem({
                          productId: data?.id,
                          cartlistState,
                          dataId: data?.id || data?._id,
                        });
                        const hasVariantsOrMar = hasVariantsOrMarination(data);

                        // If product is in cart and no variants/marination, show quantity controls
                        if (cartItem && !hasVariantsOrMar) {
                          return (
                            <QuantityControl
                              cartItem={
                                getCartItem({
                                  productId: data?.id,
                                  cartlistState,
                                  dataId: data?.id || data?._id,
                                }) || {}
                              }
                              className="bg-white border rounded-lg px-2 py-1"
                            />
                          );
                        }

                        // If has variants or marination types, show popup button
                        if (hasVariantsOrMar) {
                          return (
                            <button
                              className="flex items-center gap-2 px-4 py-2 rounded-md bg-primary-100 hover:bg-blue-700 text-white shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-75 text-sm"
                              onClick={() => togglePopup()}
                            >
                              Add to bag
                            </button>
                          );
                        }

                        return (
                          <button
                            className="flex items-center gap-2 px-4 py-2 rounded-md bg-primary-100 hover:bg-blue-700 text-white shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-75 text-sm"
                            onClick={() =>
                              addToCart({ productId: data?.id || data?._id })
                            }
                          >
                            Add to bag
                          </button>
                        );
                      })()}
                    </div>
                  ) : (
                    <button
                      type="button"
                      className="text-[#CC3E3E] border border-[#CC3E3E] bg-[#FFF1EE] font-medium rounded-3xl text-sm px-5 py-2.5 text-center"
                    >
                      Item out of stock
                    </button>
                  )}
                </div>
              </div>
            ) : (
              <div className="mt-10 flex justify-center items-center">
                <button
                  type="button"
                  className="text-[#CC3E3E] border border-[#CC3E3E] bg-[#FFF1EE]  font-medium rounded-3xl text-sm px-5 py-2.5 text-center mr-2 mb-2 0"
                >
                  Item out of stock
                </button>
              </div>
            )}
          </div>
        </div>

        <Modal
          isOpen={isOpen}
          onRequestClose={togglePopup}
          className="w-1/2 bg-white rounded-xl shadow-xl p-6 relative mx-auto mt-32 outline-none"
          overlayClassName="fixed inset-0 bg-black bg-opacity-40 flex items-start justify-center z-50"
        >
          <div className="flex justify-between items-center border-b pb-3 mb-4">
            <div className="flex-1 text-center">
              <h2 className="text-lg font-semibold text-gray-800">
                {data?.marinationTypes && data.marinationTypes.length > 0
                  ? "Choose Masala"
                  : "Choose Variant"}
              </h2>
            </div>
            <button
              onClick={togglePopup}
              className="absolute top-4 right-4 text-gray-600 hover:text-black"
            >
              <AiOutlineClose />
            </button>
          </div>

          {/* Variant selection */}
          <Variantcard
            data={data}
            selectedVariant={selectedVariant}
            setSelectedVariant={setSelectedVariant}
            cartlistState={cartlistState}
            addToCart={addToCart}
          />

          {/* Marination Selection */}
          <MarinationCard
            data={data}
            selectedVariant={selectedVariant}
            cartlistState={cartlistState}
            addToCart={addToCart}
          />
        </Modal>
      </div>
    );
  }

  // Default vertical layout
  return (
    <div
      className={`max-w-sm bg-white border  rounded-lg shadow-xl ${
        data?.stock ? "" : "opacity-75"
      }`}
    >
      <div className="relative">
        <Link href={`/product/${data?._id}`}>
          <img
            className="rounded-t-lg object-cover h-40"
            src={data?.images?.[0] || "/images/product/fish_product_image.jpg"}
            width={561}
            height={178}
            alt=""
          />
          <div className="absolute inset-0 flex flex-col justify-between">
            <div className="flex items-start justify-between">
              <span className="m-2 z-10">
                <Icons.veg isVeg={data?.isVeg} />
              </span>

              <div className="mt-3 mr-[5px] flex justify-between items-center">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    updateWIshList();
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="25"
                    height="22"
                    viewBox="0 0 25 22"
                    className={
                      wishlistState.isInWishlist(data?.id || data?._id)
                        ? "fill-red-50"
                        : "fill-white"
                    }
                  >
                    <path d="M24.7307 5.70958C24.6046 4.78435 24.2833 3.89649 23.7882 3.10477C23.2932 2.31304 22.6356 1.63552 21.859 1.1171C21.0823 0.598681 20.2045 0.251173 19.2835 0.0975893C18.3625 -0.0559947 17.4194 -0.0121443 16.5166 0.226227C15.1335 0.528113 13.8505 1.17849 12.7894 2.11562C12.0653 1.62596 11.3147 1.17688 10.5408 0.770454C6.17704 -1.28324 0.765987 1.05798 0.632507 7.00341C0.645528 9.51787 1.50248 11.9551 3.06595 13.9243C5.11671 16.646 7.69798 18.9235 10.6537 20.6194C13.272 22.1802 12.235 22.1802 14.8635 20.6194C17.8236 18.9297 20.406 16.6511 22.4513 13.9243C23.3686 12.7813 24.0437 11.4636 24.4356 10.0514C24.8274 8.63911 24.9279 7.16187 24.7307 5.70958Z" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="mb-2 ms-3">
              <span className="bg-[#E2E2E2] py-0.5 inline-flex items-center rounded px-3 text-base font-normal mt-3">
                <Icons.greenStar />
                <span className="ms-2">
                  {data?.averageRating} | {data?.totalReviews}
                </span>
              </span>
            </div>
          </div>
        </Link>
      </div>

      <div className="px-2 py-1">
        <span className="text-dark-200 text-xs font-regular">1 Pack</span>
        <Link href={`/product/${data?.id}`}>
          <h5 className="mb-2 font-semibold text-sm text-dark-250 h-10 max-h-fit line-clamp-2">
            {data?.title} | {data?.weight}
          </h5>
        </Link>

        {data?.stock?.stock !== 0 ? (
          <div>
            <div className="flex gap-3 items-center mt-1">
              {data?.offerPrice ? (
                <span>
                  <span className="text-dark-400 font-semibold text-[17px]">
                    ₹ {toFloat(data?.offerPrice)}
                  </span>
                  <span className="px-2 text-[#888888] font-light text-[13px] line-through">
                    ₹ {toFloat(data?.mrp)}
                  </span>
                  <span className="bg-gradient-to-r from-[#44ABC9] to-[#59C4C2] text-green-800 text-xs px-2 py-1 rounded">
                    {data?.mrp && data?.offerPrice
                      ? Math.round(
                          ((data.mrp - data.offerPrice) / data.mrp) * 100,
                        )
                      : 0}
                    % Off
                  </span>
                </span>
              ) : (
                <span className="text-dark-300 font-semibold text-lg">
                  ₹ {toFloat(data?.mrp)}
                </span>
              )}
            </div>

            <div className="flex justify-end mt-5 mb-2">
              {(() => {
                const cartItem = getCartItem({
                  productId: data?.id,
                  cartlistState,
                  dataId: data?.id || data?._id,
                });
                const hasVariantsOrMar = hasVariantsOrMarination(data);

                // If product is in cart and no variants/marination, show quantity controls
                if (cartItem && !hasVariantsOrMar) {
                  return (
                    <QuantityControl
                      cartItem={
                        getCartItem({
                          productId: data?.id,
                          cartlistState,
                          dataId: data?.id || data?._id,
                        }) || {}
                      }
                      className="bg-white border rounded-lg px-2 py-1"
                    />
                  );
                }

                // If has variants or marination types, show popup button
                if (hasVariantsOrMar) {
                  return (
                    <button
                      className="flex items-center gap-2 p-2 rounded-md min-w-[114px] bg-primary-100 hover:bg-blue-700 text-white shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-75 text-xs"
                      onClick={() => togglePopup()}
                    >
                      <Icons.bag />
                      Add to Bag
                    </button>
                  );
                }

                return (
                  <button
                    className="flex items-center gap-2 p-2 rounded-md min-w-[114px] bg-primary-100 hover:bg-blue-700 text-white shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-75 text-xs"
                    onClick={() =>
                      addToCart({ productId: data?.id || data?._id })
                    }
                  >
                    <Icons.bag />
                    Add to Bag
                  </button>
                );
              })()}
            </div>
          </div>
        ) : (
          <div className="mt-10 flex justify-center items-center">
            <button
              type="button"
              className="text-[#CC3E3E] border border-[#CC3E3E] bg-[#FFF1EE]  font-medium rounded-3xl text-sm px-5 py-2.5 text-center mr-2 mb-2 0"
            >
              Item out of stock
            </button>
          </div>
        )}
      </div>

      <Modal
        isOpen={isOpen}
        onRequestClose={togglePopup}
        className="w-1/2 bg-white rounded-xl shadow-xl p-6 relative mx-auto mt-32 outline-none"
        overlayClassName="fixed inset-0 bg-black bg-opacity-40 flex items-start justify-center z-50"
      >
        <div className="flex justify-between items-center border-b pb-3 mb-4">
          <div className="flex-1 text-center">
            <h2 className="text-lg font-semibold text-gray-800">
              {data?.marinationTypes && data.marinationTypes.length > 0
                ? "Choose Masala"
                : "Choose Variant"}
            </h2>
          </div>
          <button
            onClick={togglePopup}
            className="absolute top-4 right-4 text-gray-600 hover:text-black"
          >
            <AiOutlineClose />
          </button>
        </div>

        {/* Variant selection */}
        <Variantcard
          data={data}
          selectedVariant={selectedVariant}
          setSelectedVariant={setSelectedVariant}
          cartlistState={cartlistState}
          addToCart={addToCart}
        />

        {/* Marination Selection */}
        <MarinationCard
          data={data}
          selectedVariant={selectedVariant}
          cartlistState={cartlistState}
          addToCart={addToCart}
        />
      </Modal>
    </div>
  );
}

export default ProductCard;
