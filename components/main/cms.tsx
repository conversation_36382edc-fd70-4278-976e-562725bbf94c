"use client";

import { useFetch } from "@/utils/hooks/useFeatch";
import { Icms } from "@/utils/types/common";
import React, { useState, useEffect } from "react";
import apiRoutes from "@/utils/api/routes";
import apiUrl from "@/utils/api/routes";

function Cms({ slug }: { slug: string }) {
  const [data, setData] = useState<any>();

  const url: any = {
    "terms-and-condition": apiUrl.client.getTermsAndCondition,
    "cancellation-policy": apiUrl.client.getCancellationPolicy,
    "shipping-policy": apiUrl.client.getShippingPolicy,
    "privacy-policy": apiUrl.client.getPrivacyPolicy,
    "refund-policy": apiUrl.client.getRefundPolicy,
    "return-policy": apiUrl.client.getReturnPolicy,
    "about-us": apiUrl.client.getAboutUs,
  };

  useEffect(() => {
    async function getData() {
      try {
        const res = await fetch(url[slug], {
          method: "post",
          cache: "no-cache",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ limit: 100 }),
        });
        const result = await res.json();
        setData(result);
        if (!res.ok || !result?.status) {
          throw new Error("Failed to fetch data");
        }
      } catch (error) {
        console.log(error);
      }
    }
    getData();
  }, []);

  return (
    <div>
      <div className="w-full flex justify-center items-center mb-7">
        <h2 className="text-2xl font-semibold uppercase">
          {data?.data?.title || ""}
        </h2>
      </div>
      {data?.data?.content && (
        <div dangerouslySetInnerHTML={{ __html: data?.data?.content }} />
      )}
    </div>
  );
}

export default Cms;
