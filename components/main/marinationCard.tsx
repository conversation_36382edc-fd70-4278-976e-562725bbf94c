import { getCartItem } from "@/utils/helper";
import Image from "next/image";
import { Icons } from "../presentation/icons";
import QuantityControl from "./quantityControl";
import { AddToCartParams, Product } from "@/utils/types/product";

interface MarinationCardProps {
  data: Product;
  selectedVariant: string | null;
  cartlistState: any;
  addToCart: (params: AddToCartParams) => Promise<void>;
}

export default function MarinationCard({
  data,
  selectedVariant,
  cartlistState,
  addToCart,
}: MarinationCardProps) {
  return (
    <div className="mt-5">
      {data?.marinationTypes && data.marinationTypes.length > 0 && (
        <div className="space-y-3 max-h-[350px] overflow-y-auto">
          {data?.marinationTypes?.map((item: any) => (
            <div
              key={item._id}
              className="flex items-center justify-between rounded-xl p-3 bg-[#FFF7F1]"
            >
              <div className="flex items-start gap-3">
                <Image
                  src={item.image}
                  alt={item.title}
                  width={64}
                  height={64}
                  className="rounded-md object-cover"
                />
                <div>
                  <h3 className="font-medium">
                    <span>{item.title}</span>
                    {data?.defaultMasala === item._id && (
                      <span className="mx-5 px-2 rounded-md text-base bg-[#CDDEE9] text-[#2D1ED5]">
                        Recommended
                      </span>
                    )}
                  </h3>
                  <div className="flex items-center gap-1">
                    <span className="text-xs font-light">Flavor kick :</span>
                    <div className="flex items-center gap-0.5">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Image
                          key={star}
                          src={
                            star <= parseInt(item.rating || "0")
                              ? "/images/icon/flavour.png"
                              : "/images/icon/nonflavour.png"
                          }
                          alt={
                            star <= parseInt(item.rating || "0")
                              ? "flavored"
                              : "non-flavored"
                          }
                          width={12}
                          height={12}
                          className="object-contain"
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-xs font-light text-[#D25704]">
                    {item.ingredients}
                  </p>
                </div>
              </div>
              {!getCartItem({
                productId: data?.id,
                marinationId: item._id,
                variantId: selectedVariant || undefined,
                cartlistState,
                dataId: data?.id || data?._id,
              }) ? (
                <button
                  className="flex items-center gap-2 p-2 min-w-[114px] bg-[#f0b051] text-white rounded-md text-xs font-medium hover:bg-[#FFA727]-700 text-white shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-[#FFA727]-300 focus:ring-opacity-75"
                  onClick={() => {
                    addToCart({
                      productId: data?.id || data?._id,
                      marinationId: item._id,
                    });
                  }}
                >
                  <Icons.bag />
                  Add to Bag
                </button>
              ) : (
                <QuantityControl
                  cartItem={
                    getCartItem({
                      productId: data?.id,
                      marinationId: item._id,
                      variantId: selectedVariant || undefined,
                      cartlistState,
                      dataId: data?.id || data?._id,
                    }) || {}
                  }
                  className="bg-white"
                />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
