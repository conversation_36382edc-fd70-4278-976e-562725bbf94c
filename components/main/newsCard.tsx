import React from "react";
import Image from "next/image";
import { Blog } from "@/utils/types/common";

function NewsCard({ item }: { item: Blog }) {
  return (
    <>
      <Image
        src={item?.image || "/images/other/news.png"}
        className=" h-80 object-cover items-end"
        alt="product"
        width={516}
        height={344}
      />
      <div className="mt-2">
        <span className=" text-sm font-normal text-dark-450 mt-10">
          {" "}
          {new Date(item?.createdAt)?.toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "long",
            year: "numeric",
          })}
          , Delhi
        </span>
      </div>
      <h5 className="line-clamp-5 text-xl font-semibold text-dark-50">
        {item?.title}{" "}
      </h5>
      <p className="mt-3 text-sm font-normal text-[#3d3d3d] line-clamp-6">
        {item?.shortDescription}
      </p>
      <button className="text-xs font-semibold">Read more</button>
    </>
  );
}

export default NewsCard;
