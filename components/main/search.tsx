"use client";
import { useEffect, useRef } from "react";
import { usePathname, useRouter } from "next/navigation";

function Search() {
  const queryRef = useRef<any>("");
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!pathname.includes("search")) {
      queryRef.current.value = "";
    }
  }, [pathname]);

  const submitHandler = (event: any) => {
    event.preventDefault();
    router.push(`/search?key=${queryRef?.current?.value}`);
  };

  const submit = () => {
    router.push(`/search?key=${queryRef?.current?.value}`);
  };

  return (
    <div>
      <div className="w-96 mx-5">
        <form className="flex items-center" onSubmit={submitHandler}>
          <label className="sr-only">Search</label>
          <div className="relative w-full">
            <input
              ref={queryRef}
              type="text"
              id="search"
              className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-96 p-2.5"
              placeholder="Hey, What you looking for?"
              required
            />
            <button
              onClick={submit}
              type="button"
              className="absolute inset-y-0 right-0 flex items-center pr-3"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M14.0226 15.8776C12.3033 17.2462 10.1249 17.904 7.93549 17.7156C5.74606 17.5273 3.71205 16.5071 2.25174 14.865C0.791431 13.2229 0.015841 11.0836 0.0845236 8.8872C0.153206 6.69076 1.06094 4.60413 2.621 3.05645C4.18105 1.50878 6.27485 0.617721 8.47177 0.566542C10.6687 0.515363 12.8017 1.30795 14.4321 2.7813C16.0626 4.25465 17.0665 6.29674 17.2374 8.4876C17.4082 10.6785 16.7331 12.8515 15.3509 14.5599L18.5977 17.7224C18.7247 17.8384 18.841 17.9654 18.9456 18.1019C19.0811 18.2791 19.1482 18.4991 19.1347 18.7218C19.1211 18.9444 19.0278 19.1547 18.8718 19.3142C18.716 19.4862 18.5002 19.592 18.2687 19.6096C18.0373 19.6273 17.8079 19.5555 17.6278 19.4091C17.2483 19.0928 16.911 18.7344 16.5737 18.3549L14.0226 15.8776ZM1.91015 9.00437C1.87723 10.3319 2.23862 11.6394 2.94869 12.7615C3.65876 13.8837 4.6856 14.7701 5.8994 15.3087C7.11319 15.8474 8.45944 16.014 9.76793 15.7877C11.0764 15.5613 12.2885 14.952 13.2507 14.0369C14.213 13.1218 14.8823 11.9419 15.1741 10.6464C15.4659 9.35091 15.3671 7.998 14.89 6.7587C14.413 5.5194 13.5792 4.44935 12.4942 3.68384C11.4091 2.91833 10.1214 2.49173 8.79387 2.45796C7.01511 2.42105 5.29383 3.08823 4.0046 4.31429C2.71537 5.54034 1.96259 7.22601 1.91015 9.00437Z"
                  fill="#414141"
                />
              </svg>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Search;
