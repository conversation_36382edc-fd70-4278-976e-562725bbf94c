"use client";

import React from "react";
import { Category } from "@/utils/types/category";
import { useRouter } from "next/navigation";

function CategorySection({ data }: { data: Category[] }) {
  const router = useRouter();

  return (
    <div className="flex bg-[#EFEEFF] border border-gray-300 items-center gap-2 px-2 rounded-lg">
      <svg
        width="25"
        height="25"
        viewBox="0 0 25 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.7793 14.5918H9.49902C9.77065 14.5918 10.033 14.6857 10.2412 14.8564L10.3271 14.9336C10.5466 15.153 10.6689 15.4514 10.6689 15.7617V22.4814C10.6689 22.7916 10.5464 23.0892 10.3271 23.3086C10.1077 23.528 9.80933 23.6514 9.49902 23.6514H2.7793C2.50793 23.6513 2.24622 23.5571 2.03809 23.3867L1.95215 23.3086C1.73275 23.0892 1.6094 22.7917 1.60938 22.4814V15.7617C1.60938 15.4902 1.70341 15.2277 1.87402 15.0195L1.95215 14.9336C2.17152 14.7144 2.46915 14.5918 2.7793 14.5918ZM19.5791 14.5918C20.7056 14.5918 21.7891 15.011 22.6201 15.7637L22.7822 15.918C23.6317 16.7674 24.1093 17.9198 24.1094 19.1211C24.1094 19.905 23.9057 20.6736 23.5215 21.3525L23.3457 21.6377C22.8479 22.3827 22.1402 22.9638 21.3125 23.3066C20.5883 23.6065 19.8006 23.7124 19.0264 23.6172L18.6953 23.5645C17.9267 23.4115 17.2129 23.0619 16.6221 22.5527L16.376 22.3242C15.8218 21.7699 15.4222 21.0828 15.2139 20.3311L15.1367 20.0049C14.9838 19.2361 15.033 18.4429 15.2773 17.7021L15.3945 17.3877C15.6946 16.6635 16.1761 16.0309 16.791 15.5508L17.0625 15.3545C17.8074 14.8568 18.6833 14.5919 19.5791 14.5918ZM2.7793 1.15137H9.49902C9.77056 1.15137 10.033 1.2454 10.2412 1.41602L10.3271 1.49414C10.5464 1.71351 10.6689 2.01114 10.6689 2.32129V9.04102C10.6689 9.31264 10.575 9.57497 10.4043 9.7832L10.3271 9.86914C10.1077 10.0886 9.80933 10.2109 9.49902 10.2109H2.7793C2.50784 10.2109 2.24625 10.1168 2.03809 9.94629L1.95215 9.86914C1.73273 9.64972 1.60938 9.35132 1.60938 9.04102V2.32129C1.60939 2.04992 1.7036 1.78821 1.87402 1.58008L1.95215 1.49414C2.14421 1.30208 2.39611 1.18376 2.66406 1.15723L2.7793 1.15137ZM16.2197 1.15137H22.9395C23.2108 1.15139 23.4725 1.24559 23.6807 1.41602L23.7666 1.49414C23.986 1.71354 24.1094 2.01101 24.1094 2.32129V9.04102C24.1094 9.31255 24.0153 9.575 23.8447 9.7832L23.7666 9.86914C23.5472 10.0884 23.2496 10.2109 22.9395 10.2109H16.2197C15.9094 10.2109 15.611 10.0886 15.3916 9.86914C15.1722 9.64972 15.0498 9.35132 15.0498 9.04102V2.32129C15.0498 2.04983 15.1439 1.78824 15.3145 1.58008L15.3916 1.49414C15.611 1.27472 15.9094 1.15137 16.2197 1.15137Z"
          stroke="#3559A8"
          strokeWidth="1.5"
        />
      </svg>
      <select
        className="bg-[#EFEEFF] text-gray-900 text-sm   block w-full p-2.5 focus-visible:outline-none"
        onChange={(event) => {
          const value = event?.target?.value;
          if (value === "all") {
            router.push("/");
          } else {
            router.push(`/category/${value}`);
          }
        }}
      >
        <option value="all">Categories</option>
        {data?.map((item, index: number) => (
          <option key={index} value={item?.id}>
            {item?.title}
          </option>
        ))}
      </select>
    </div>
  );
}

export default CategorySection;
