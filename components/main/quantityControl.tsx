import React, { useEffect, useState } from "react";
import apiRoutes from "@/utils/api/routes";
import { useCartStore } from "@/utils/store/cart";
import { toast } from "react-toastify";
import { Icons } from "../presentation/icons";

function QuantityControl({
  cartItem,
  className = "",
}: {
  cartItem: any;
  className?: string;
}) {
  const [qty, setqty] = useState(cartItem?.quantity);
  const [isIncreasing, setisIncreasing] = useState(false);
  const [isDecreasing, setisDecreasing] = useState(false);
  const cartStore = useCartStore();

  const [token, setToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
  }, []);

  // Update local quantity when cartItem changes
  useEffect(() => {
    setqty(cartItem?.quantity);
  }, [cartItem?.quantity]);

  const updateCart = async (isIn: boolean) => {
    const cartId = cartItem?.id;
    try {
      isIn ? setisIncreasing(true) : setisDecreasing(true);
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      headers.append("Authorization", `Bearer ${token}`);
      const response = await fetch(
        isIn ? apiRoutes.client.increaseCart : apiRoutes.client.decreaseCart,
        {
          method: "POST",
          body: JSON.stringify({ cart: cartId }),
          headers: headers,
        },
      );
      const result = await response.json();
      if (result?.status) {
        isIn ? setqty(qty + 1) : setqty(qty - 1);
        cartStore.setCartList(result?.data);
        toast(
          <>
            <Icons.greenbag className="inline-block mr-2" />
            <span className="px-2">
              Item {isIn ? "added to" : "removed from"} bag Successfully
            </span>
          </>,
        );
      }
    } catch (error) {
      console.log(error);
      toast.error("Failed to add product card!");
    } finally {
      setisIncreasing(false);
      setisDecreasing(false);
    }
  };

  return (
    <div className={`inline-flex items-center gap-2 ${className}`}>
      <button
        onClick={() => updateCart(false)}
        disabled={isDecreasing}
        className="w-8 h-8 bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 text-gray-700 rounded-full flex items-center justify-center text-lg font-semibold transition-colors"
      >
        -
      </button>
      <span className="text-sm font-semibold min-w-[20px] text-center">
        {qty}
      </span>
      <button
        onClick={() => updateCart(true)}
        disabled={isIncreasing}
        className="w-8 h-8 bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 text-gray-700 rounded-full flex items-center justify-center text-lg font-semibold transition-colors"
      >
        +
      </button>
    </div>
  );
}

export default QuantityControl;
