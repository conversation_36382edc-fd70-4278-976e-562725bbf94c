import { getCartItem } from "@/utils/helper";
import Image from "next/image";
import { Icons } from "../presentation/icons";
import QuantityControl from "./quantityControl";
import { AddToCartParams, Product } from "@/utils/types/product";

interface VariantcardProps {
  data: Product;
  selectedVariant: string | null;
  setSelectedVariant: React.Dispatch<React.SetStateAction<string | null>>;
  cartlistState: any;
  addToCart: (params: AddToCartParams) => Promise<void>;
}

export default function Variantcard({
  data,
  selectedVariant,
  setSelectedVariant,
  cartlistState,
  addToCart,
}: VariantcardProps) {
  return (
    <div className="mt-5">
      {data?.variants &&
        data.variants.filter((item: any) => item.status === "Active").length >
          0 && (
          <div className="mb-4">
            {data?.marinationTypes && data.marinationTypes.length > 0 ? (
              // GRID View with variant selection when marinationTypes exist
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-h-[350px] overflow-y-auto">
                {data.variants.map((v: any) => (
                  <div
                    key={v._id}
                    className={`cursor-pointer p-3 rounded-xl transition-colors ${
                      selectedVariant === v?.id?._id
                        ? "border-2 border-blue-500"
                        : "bg-[#FFF7F1] hover:border-2 hover:border-blue-300"
                    }`}
                    onClick={() => setSelectedVariant(v?.id?._id)}
                  >
                    <div className="flex flex-col items-center gap-2 text-center">
                      <Image
                        src={v?.id?.image}
                        alt={v?.id?.title}
                        width={64}
                        height={64}
                        className="rounded-md object-cover"
                      />
                      <div>
                        <h3 className="font-medium text-sm">{v?.id?.title} (₹{v?.id?.value})</h3>
                        <p className="text-xs text-gray-600 line-clamp-2">
                          {v?.id?.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // LIST View (original) when no marinationTypes
              <div className="space-y-2 max-h-[350px] overflow-y-auto">
                {data.variants.map((v: any) => (
                  <div
                    key={v._id}
                    className={`flex items-center justify-between p-3 rounded-xl ${
                      selectedVariant === v?.id?._id
                        ? "border-2 border-blue-500"
                        : "bg-[#FFF7F1]"
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <Image
                        src={v?.id?.image}
                        alt={v?.id?.title}
                        width={64}
                        height={64}
                        className="rounded-md object-cover"
                      />
                      <div>
                        <h3 className="font-medium">{v?.id?.title}</h3>
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {v?.id?.description}
                        </p>
                      </div>
                    </div>
                    <div>
                      {!getCartItem({
                        productId: data?.id,
                        variantId: v?.id?._id,
                        cartlistState,
                        dataId: data?.id || data?._id,
                      }) ? (
                        <button
                          className="flex items-center gap-2 p-2 min-w-[114px] bg-primary-100 text-white rounded-md text-xs font-medium hover:bg-blue-700 text-white shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-75"
                          onClick={() =>
                            addToCart({
                              productId: data?.id || data?._id,
                              variantId: v?.id?._id,
                            })
                          }
                        >
                          <Icons.bag />
                          Add to Bag
                        </button>
                      ) : (
                        <QuantityControl
                          cartItem={
                            getCartItem({
                              productId: data?.id,
                              variantId: v?.id?._id,
                              cartlistState,
                              dataId: data?.id || data?._id,
                            }) || {}
                          }
                          className="bg-white"
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
    </div>
  );
}
