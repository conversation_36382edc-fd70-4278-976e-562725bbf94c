import React, { useState, useEffect } from "react";

const CountdownTimer = ({ endTime }: any) => {
  const calculateTimeLeft = () => {
    const currentDate: any = new Date();
    const difference: any = endTime - currentDate;
    let timeLeft = {};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    }

    return timeLeft;
  };

  const [timeLeft, setTimeLeft] = useState<any>(calculateTimeLeft());

  useEffect(() => {
    const timer = setTimeout(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearTimeout(timer);
  });

  const timerComponents: any = [];

  Object.keys(timeLeft).forEach((interval, index) => {
    if (!timeLeft[interval]) {
      return;
    }

    timerComponents.push(
      <span key={interval}>
        {timeLeft[interval]}
        {index == 3 ? "" : ":"}
      </span>,
    );
  });

  return (
    <div>
      {timerComponents.length ? (
        <span className="bg-gradient-to-r from-[#3092D0] to-[#59C4C2] py-0.5 px-4 text-white tracking-widest rounded-lg w-fit">
          {" "}
          {timerComponents}{" "}
        </span>
      ) : (
        <span>Time's up!</span>
      )}
    </div>
  );
};

export default CountdownTimer;
