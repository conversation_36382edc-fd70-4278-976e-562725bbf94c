import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Blog } from "@/utils/types/common";

function BlogCard({ item }: { item: Blog }) {
  return (
    <div>
      <Link href={`/blog/${item?.id}`}>
        <Image
          src={item?.image || "/images/other/blog.png"}
          className="rounded-2xl object-cover items-end h-60"
          alt="product"
          width={516}
          height={344}
        />
        <div className="flex items-center space-x-4 mt-2">
          <img
            className="w-10 h-10 rounded-full"
            src={item?.authorImage || "/images/people/peoplpe.png"}
            alt=""
          />
          <div className="font-medium ">
            <div className="text-base font-semibold text-dark-250">
              {item?.authorName}
            </div>
            <div className=" text-[#878787] text-xs font-medium">
              {" "}
              {new Date(item?.createdAt)?.toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "long",
                year: "numeric",
              })}
            </div>
          </div>
        </div>
        <div className="mt-3">
          <h6 className="text-dark-10 text-base font-semibold">
            {item?.title}
          </h6>
          <p className="mt-2 text-dark-10 text-xs font-normal line-clamp-4">
            {item?.shortDescription}{" "}
          </p>
          Read More
        </div>
      </Link>
    </div>
  );
}

export default BlogCard;
