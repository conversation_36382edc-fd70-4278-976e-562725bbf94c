import Link from "next/link";
import React from "react";
import CountdownTimer from "./countDown";

interface TitleProps {
  title: string;
  subtitle?: string;
  timer?: any;
  page?: string;
}

function Title({ title, timer, subtitle, page }: TitleProps) {
  return (
    <div className="flex justify-between items-baseline px-4">
      <div className="flex gap-10 items-baseline">
        <div>
          <h5 className="text-dark-10 text-[32px] font-medium">{title}</h5>
          {subtitle && (
            <span className="text-dark-450 text-[22px] font-regular">
              {subtitle}
            </span>
          )}
        </div>
        {timer && <CountdownTimer endTime={new Date(timer)} />}
      </div>
      {page && (
        <Link
          href={page}
          className="bg-light-100 py-2 px-3  text-dark-100 font-normal text-sm rounded-full flex gap-2 items-center"
        >
          View all
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="6"
            height="9"
            viewBox="0 0 6 9"
            fill="none"
          >
            <path
              d="M1.15507 0.841445C1.06308 0.929121 1.0061 1.04726 0.994766 1.17384C0.983432 1.30042 1.01846 1.4268 1.09341 1.52943C1.13244 1.5884 1.18103 1.64046 1.23717 1.68346L4.23537 4.68185L1.22689 7.68024C1.22689 7.73158 1.12419 7.79319 1.07285 7.86507C1.01144 7.96851 0.986097 8.08938 1.00066 8.2088C1.01522 8.32821 1.06885 8.43944 1.15332 8.52509C1.23779 8.61074 1.34835 8.66599 1.46754 8.68219C1.58673 8.6984 1.70782 8.67465 1.81209 8.61467C1.88715 8.56473 1.95615 8.50614 2.01752 8.4401L5.30312 5.1542C5.37354 5.0958 5.43164 5.02395 5.47396 4.94285C5.51627 4.86174 5.54192 4.77301 5.54954 4.68185C5.54173 4.59219 5.51593 4.50504 5.47358 4.42563C5.43123 4.34622 5.37323 4.2762 5.30312 4.21977L2.00724 0.964667C1.94888 0.902445 1.88333 0.847301 1.81209 0.800371C1.71512 0.729106 1.5963 0.694177 1.47619 0.701684C1.35608 0.709191 1.24242 0.758659 1.15507 0.841445Z"
              fill="#686868"
            />
          </svg>
        </Link>
      )}
    </div>
  );
}

export default Title;
