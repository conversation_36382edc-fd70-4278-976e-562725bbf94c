import { STATUS_HISTORY } from "@/utils/types/common";
import React from "react";

interface Props {
  orderStatus: number | any;
  statusHistory: STATUS_HISTORY;
}

function TimeLine({ orderStatus, statusHistory }: Props) {
  return (
    <div>
      <ul className="relative flex flex-row   mx-auto">
        <li className="shrink basis-0 flex-1 group">
          <div className="mt-3">
            <span className="block font-medium text-[#1AAF6F] text-[0.6rem]">
              Order Confrmed
            </span>
          </div>
          <div className="min-w-[28px] min-h-[28px] w-full inline-flex items-center text-xs align-middle">
            <span className="w-7 h-7 flex justify-center items-center flex-shrink-0 bg-[#1AAF6F] font-medium text-gray-800 rounded-full "></span>
            <div
              className={`w-full h-px flex-1 bg-[#1AAF6F] group-last:hidden `}
            ></div>
          </div>
          <div className="mt-3">
            {statusHistory?.ordered_on && (
              <span className="block font-medium text-dark-450 text-[0.6rem]">
                {new Date(statusHistory?.ordered_on)?.toLocaleDateString()}
              </span>
            )}
          </div>
        </li>

        <li className="shrink basis-0 flex-1 group">
          <div className="mt-3">
            <span className="block font-medium text-[#1AAF6F] text-[0.6rem]">
              Shipped
            </span>
          </div>
          <div className="min-w-[28px] min-h-[28px] w-full inline-flex items-center text-xs align-middle">
            <span
              className={`${[3, 4, 5, 6].includes(orderStatus) ? "bg-[#1AAF6F]" : " bg-slate-200"} w-7 h-7 flex justify-center items-center flex-shrink-0  font-medium text-gray-800 rounded-full`}
            ></span>
            <div
              className={`${[3, 4, 5, 6].includes(orderStatus) ? "bg-[#1AAF6F]" : " bg-slate-200"} w-full h-px flex-1 group-last:hidden1`}
            ></div>
          </div>
          <div className="mt-3">
            {statusHistory?.shipped_on && (
              <span className="block font-medium text-dark-450 text-[0.6rem]">
                {new Date(statusHistory?.shipped_on)?.toLocaleDateString()}
              </span>
            )}
          </div>
        </li>

        <li className="shrink basis-0 flex-1 group">
          <div className="mt-3">
            <span className="block font-medium text-[#1AAF6F] text-[0.6rem]">
              Out of Delivery
            </span>
          </div>
          <div className="min-w-[28px] min-h-[28px] w-full inline-flex items-center text-xs align-middle">
            <span
              className={`${[4, 5, 6].includes(orderStatus) ? "bg-[#1AAF6F]" : " bg-slate-200"} w-7 h-7 flex justify-center items-center flex-shrink-0  font-medium text-gray-800 rounded-full`}
            ></span>
            <div
              className={`${[4, 5, 6].includes(orderStatus) ? "bg-[#1AAF6F]" : " bg-slate-200"} w-full h-px flex-1 group-last:hidden1`}
            ></div>
          </div>
          <div className="mt-3">
            {statusHistory?.delivered_on && (
              <span className="block font-medium text-dark-450 text-[0.6rem]">
                {new Date(statusHistory?.delivered_on)?.toLocaleDateString()}
              </span>
            )}
          </div>
        </li>

        <li className="shrink basis-0 flex-1 group">
          <div className="mt-3">
            <span className="block font-medium text-[#1AAF6F] text-[0.6rem]">
              Delivered
            </span>
          </div>
          <div className="min-w-[28px] min-h-[28px] w-full inline-flex items-center text-xs align-middle">
            <span
              className={`${[5, 6].includes(orderStatus) ? "bg-[#1AAF6F]" : " bg-slate-200"} w-7 h-7 flex justify-center items-center flex-shrink-0  font-medium text-gray-800 rounded-full`}
            ></span>
            <div className={` w-full h-px flex-1 group-last:hidden1`}></div>
          </div>
          <div className="mt-3">
            {statusHistory?.delivered_on && (
              <span className="block font-medium text-dark-450 text-[0.6rem]">
                {new Date(statusHistory?.delivered_on)?.toLocaleDateString()}
              </span>
            )}
          </div>
        </li>
      </ul>
    </div>
  );
}

export default TimeLine;
