import { Product } from "@/utils/types/product";
import React, { useState } from "react";
import productConstant from "@/utils/constant";
import { useSession } from "next-auth/react";
import apiRoutes from "@/utils/api/routes";
import { Icons } from "../presentation/icons";
import Link from "next/link";

function ProductCartCard({ data }: { data: Product }) {
  const { data: session } = useSession();

  const [qty, setqty] = useState(0);
  const [isIncreasing, setisIncreasing] = useState(false);
  const [isDecreasing, setisDecreasing] = useState(false);

  const updateCart = async (cartId: any, isIn: boolean) => {
    try {
      isIn ? setisIncreasing(true) : setisDecreasing(true);
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      headers.append("Authorization", `Bearer ${session?.token}`);
      const response = await fetch(
        isIn ? apiRoutes.client.increaseCart : apiRoutes.client.decreaseCart,
        {
          method: "POST",
          body: JSON.stringify({ cart: cartId }),
          headers: headers,
        },
      );
      const result = await response.json();
      if (result?.status) {
        isIn ? setqty(qty + 1) : setqty(qty - 1);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setisIncreasing(false);
      setisDecreasing(false);
    }
  };

  return (
    <div className="h-10">
      {data?.productType !== productConstant.PRODUCT_TYPE.parentProduct ? (
        <>
          {qty ? (
            <div className="flex items-end justify-end">
              <div className="  rounded-xl flex justify-between items-center w-24">
                <button
                  disabled={isDecreasing}
                  onClick={() => updateCart(data?._id || data?.id, false)}
                  className={`${data?.isVeg ? "bg-[#1AAF6F] " : "bg-[#FBB03B]"}   w-7 h-7 text-white rounded-lg disabled:bg-transparent`}
                >
                  {isDecreasing ? <Icons.loading /> : "-"}
                </button>
                <div className="flex flex-col items-center justify-center">
                  <h6 className="leading-none">{qty}</h6>
                  <h6 className="text-[0.5rem] font-normal">Pack</h6>
                </div>
                <button
                  onClick={() => updateCart(data?.id || data?._id, true)}
                  disabled={isIncreasing}
                  className={`${data?.isVeg ? "bg-[#1AAF6F] " : "bg-[#FBB03B]"}   w-7 h-7 text-white rounded-lg disabled:bg-transparent`}
                >
                  {isIncreasing ? <Icons.loading /> : "+"}
                </button>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setqty(1)}
              className={`${data?.isVeg ? "bg-[#D6FFDC]" : "bg-[#FFF1EE]"} rounded-lg`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="29"
                height="30"
                viewBox="0 0 29 30"
                fill="none"
              >
                <path d="M21.7063 0.913574H6.91056C3.13389 0.913574 0.0722961 3.97541 0.0722961 7.75237V22.5492C0.0722961 26.3262 3.13389 29.388 6.91056 29.388H21.7063C25.4829 29.388 28.5445 26.3262 28.5445 22.5492V7.75237C28.5445 3.97541 25.4829 0.913574 21.7063 0.913574Z" />
                <path
                  d="M18.8416 15.6899H14.8783V19.6946H13.718V15.6899H9.76495V14.663H13.718V10.6583H14.8783V14.663H18.8416V15.6899Z"
                  fill={data?.isVeg ? "#1AAF6F" : "#F7796F"}
                />
              </svg>
            </button>
          )}
        </>
      ) : (
        <div>
          <Link
            href={`/product/${data?.id}`}
            className="text-dark-500 bg-[#FBB03B] font-medium  rounded-lg text-sm px-5 py-2 text-center inline-flex items-center justify-between gap-3"
          >
            Choose
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="4"
              height="9"
              viewBox="0 0 4 9"
              fill="none"
            >
              <path
                d="M3.68616 4.05708L0.985756 0.709555C0.946162 0.651033 0.897708 0.599069 0.842057 0.555535C0.806363 0.52452 0.764349 0.501562 0.718975 0.488255C0.673602 0.474948 0.62595 0.471591 0.579161 0.478415C0.532372 0.485239 0.487553 0.50209 0.44787 0.527802C0.408186 0.553514 0.374517 0.587504 0.349166 0.627422C0.296186 0.704462 0.267822 0.795739 0.267822 0.889241C0.267822 0.982743 0.296186 1.07405 0.349166 1.15109L0.462158 1.30511L2.26921 3.57446C2.45403 3.7901 2.62861 4.016 2.81343 4.24191L2.99824 4.46781L2.93664 4.5397L0.451881 7.62024L0.338951 7.78454C0.299238 7.8665 0.283013 7.95786 0.292075 8.04849C0.301137 8.13912 0.33512 8.22548 0.390277 8.29796C0.42157 8.3424 0.463103 8.37864 0.511353 8.40366C0.559603 8.42868 0.613111 8.44173 0.667461 8.4417C0.74223 8.44261 0.814943 8.41715 0.872827 8.36982C0.937354 8.32578 0.993108 8.27005 1.03714 8.20552L3.68616 4.81694C3.73773 4.76801 3.77885 4.70909 3.80692 4.64377C3.83499 4.57845 3.84941 4.50811 3.84941 4.43701C3.84941 4.36592 3.83499 4.29557 3.80692 4.23025C3.77885 4.16493 3.73773 4.10601 3.68616 4.05708Z"
                fill="#3D3D3D"
              />
            </svg>
          </Link>
        </div>
      )}
    </div>
  );
}

export default ProductCartCard;
