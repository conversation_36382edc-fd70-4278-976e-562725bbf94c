import React, { useState } from "react";
import Image from "next/image";
import { Product } from "@/utils/types/product";
import apiRoutes from "@/utils/api/routes";
import { Icons } from "../presentation/icons";
import Link from "next/link";
import { useCartStore } from "@/utils/store/cart";
import { useToast } from "@/components/ui/use-toast";

function MultiProduct({
  child,
  close,
  token,
}: {
  child: Product;
  close: () => void;
  token: string;
}) {
  const { toast } = useToast();

  const toFloat = (number: any) => (Math.round(number * 100) / 100).toFixed(2);
  const [qty, setqty] = useState(1);
  const [isaddingToCart, setaddingToCart] = useState(false);

  const cartState = useCartStore();

  const addToCart = async (cart: string) => {
    try {
      setaddingToCart(true);
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      headers.append("Authorization", `Bearer ${token}`);
      const response = await fetch(apiRoutes.client.addToCart, {
        method: "POST",
        body: JSON.stringify({ id: cart }),
        headers: headers,
      });
      const result = await response.json();
      if (result?.status) {
        cartState.setCartList(result?.data || []);
        toast({ content: "Added to cart" });
        close();
      }
    } catch (error) {
      console.log(error);
      toast({ content: "Failed to add" });
      setaddingToCart(false);
    } finally {
      setaddingToCart(false);
    }
  };

  return (
    <div className="grid grid-cols-2 gap-7">
      <div>
        <Image
          src={child?.image || "/images/product/product3.png"}
          className="rounded-lg object-cover items-end h-44"
          alt="product"
          width={516}
          height={344}
        />
      </div>
      <div className="h-44 overflow-hidden">
        <h5 className=" text-dark-400 text-base font-semibold">
          {child?.title}
        </h5>
        <p className="mt-2 text-[#848484] text-xs font-normal">{child?.hint}</p>
        <div className="mt-3 flex justify-start items-center gap-5">
          <span>
            <span className="text-[#666] text-sm font-normal me-1">
              {" "}
              Final price :{" "}
            </span>
            <span className="text-dark-300 font-semibold text-base">
              ₹ {toFloat(child?.price * qty)}
            </span>
          </span>
          <span>
            <div className="border border-[#D8D8D8] rounded-lg flex justify-between items-center w-32">
              <button
                className="bg-[#FFF1DE] p-2 px-3 m-1 rounded-lg text-[#FBB03B]"
                onClick={() => setqty(qty - 1)}
              >
                -
              </button>
              <span>{qty}</span>
              <button
                className="bg-[#FFF1DE] text-[#FBB03B] p-2 px-3 m-1 rounded-lg"
                onClick={() => setqty(qty + 1)}
              >
                +
              </button>
            </div>
          </span>
        </div>
        <div className="mt-6">
          {cartState?.carts?.includes(child?.id || child?._id) ? (
            <Link
              href="/cart"
              className="w-30 text-white bg-[#13995F] disabled:bg-green-600   rounded-lg text-xs font-semibold px-5 py-2.5 text-center inline-flex items-center gap-3"
            >
              Go TO CART
            </Link>
          ) : (
            <button
              onClick={() => addToCart(child?.id || child?._id)}
              disabled={isaddingToCart}
              type="button"
              className="w-30 text-white bg-[#13995F]  disabled:bg-green-700  rounded-lg text-xs font-semibold px-5 py-2.5 text-center inline-flex items-center gap-2"
            >
              ADD TO CART {isaddingToCart && <Icons.loading />}
            </button>
          )}
          {/* 
                    <button onClick={() => addToCart(child?.id || child?._id)} disabled={isaddingToCart} type="button" className="w-30 text-white bg-[#13995F]  disabled:bg-green-700  rounded-lg text-xs font-semibold px-5 py-2.5 text-center inline-flex items-center gap-2">
                        ADD TO CART {isaddingToCart && <Icons.loading />}
                    </button> */}
        </div>
      </div>
    </div>
  );
}

export default MultiProduct;
