services:
  customer-app:
    container_name: customer-app
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "4000:4000"
    volumes:
      - /usr/src/app/customer/node_modules
    environment:
      - VIRTUAL_HOST=customer.staging.ficean.com
      - LETSENCRYPT_HOST=customer.staging.ficean.com
      - LETSENCRYPT_EMAIL=<EMAIL>
    networks:
      # - nextjs-network
      - app-network

networks:
  # nextjs-network:
  # driver: bridge
  app-network:
    external: true
