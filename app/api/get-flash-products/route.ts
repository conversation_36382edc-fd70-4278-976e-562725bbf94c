// app/api/get-flash-products/route.ts

import { NextResponse } from "next/server";
import apiUrl from "@/utils/api/routes";

export async function POST(req: Request) {
  const { limit } = await req.json();
  const backendApiUrl = `${process.env.BACKEND_URL}/api/flash`;
  // Call your backend API from here (server-side only)
  const res = await fetch(backendApiUrl, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ limit }),
  });

  const result = await res.json();

  return NextResponse.json(result);
}
