import NextAuth from "next-auth/next";
import { authOptions } from "@/utils/auth/auth";

// async function refreshToken(token: any): Promise<JWT> {
//     const res = await fetch(Backend_URL + "/auth/refresh", {
//         method: "POST",
//         headers: {
//             authorization: `Refresh ${token?.backendTokens?.refreshToken}`,
//         },
//     });

//     const response = await res.json();

//     return {
//         ...token,
//         backendTokens: response,
//     };
// }

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
