import React from "react";
import Image from "next/image";
import Link from "next/link";

function NotFound() {
  return (
    <div className="h-[80vh] flex justify-center items-center">
      <div className="text-center mx-auto">
        <Image
          src="/images/other/nodelivery.png"
          className="mx-auto object-contain"
          width={238}
          height={261}
          alt="notfound"
        />
        <h2 className="font-semibold text-[35px] mt-5 mb-10">
          Sorry, We are not here yet !
        </h2>
        <Link
          href="/"
          className="bg-primary-100 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          Search another Location
        </Link>
      </div>
    </div>
  );
}

export default NotFound;
