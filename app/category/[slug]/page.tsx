"use client";

import ProductCard from "@/components/main/productCard";
import React, { useState, useEffect } from "react";
import apiUrl from "@/utils/api/routes";
import { Product } from "@/utils/types/product";
import getData from "@/utils/api/getData";
import Nodata from "@/components/presentation/noData";

interface Data {
  product: Product[];
  category: { title: string };
}

async function Page({ params }: { params: { slug: string } }) {
  // const data: Data = await getData({
  //   url: apiUrl.server.getProductsByCategory,
  //   body: { id: params?.slug },
  // });

  const [data, setData] = useState<any>();

  useEffect(() => {
    async function getData() {
      try {
        const res = await fetch(apiUrl.client.getProductsByCategory, {
          method: "post",
          cache: "no-cache",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ id: params?.slug }),
        });
        const result = await res.json();
        setData(result?.data);
        if (!res.ok || !result?.status) {
          // This will activate the closest `error.js` Error Boundary
          throw new Error("Failed to fetch data");
        }
      } catch (error) {
        console.log(error);
      }
    }
    getData();
  }, []);

  return (
    <main className="w-full bg-[#FCFCFC]">
      <div className="container py-10">
        <h4 className="text-dark-50 text-4xl font-semibold">
          {data?.category?.title}
        </h4>
        {!!data?.products?.data?.length ? (
          <div className="mt-7 grid gap-3 grid-cols-5">
            {data?.products?.data?.map((item: any, number: number) => (
              <ProductCard key={number} data={item} />
            ))}
          </div>
        ) : (
          <Nodata />
        )}
      </div>
    </main>
  );
}

export default Page;
