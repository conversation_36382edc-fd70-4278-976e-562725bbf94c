"use client";
import { useEffect, useState } from "react";
const url = process.env.NEXT_PUBLIC_BACKEND_URL;
import apiUrl from "@/utils/api/routes";

function FormRow({
  label,
  children,
}: {
  label: string;
  children: React.ReactNode;
}) {
  return (
    <div className="flex items-center gap-4">
      <label className="w-1/3 font-medium text-gray-700 capitalize">
        {label}
      </label>
      <div className="w-2/3">{children}</div>
    </div>
  );
}

interface CheckoutProps {
  searchParams: { amount?: string };
}

export default function CheckoutPage({ searchParams }: CheckoutProps) {
  const amount = Number(searchParams.amount || 1);
  const [token, setToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
  }, []);

  const [form, setForm] = useState({
    order_id: "",
    amount: 0,
    client_id: "",
  });

  useEffect(() => {
    const orderId = `order_${Date.now()}`;
    setForm({
      order_id: orderId,
      amount,
      client_id: "hdfcmaster",
    });
  }, [amount]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new URLSearchParams();
    formData.append("order_id", form.order_id);
    formData.append("amount", form.amount.toString());
    formData.append("payment_page_client_id", form.client_id);
    formData.append("currency", "INR");
    formData.append("redirect_url", `/backend/handlePaymentResponse`);

    const header = new Headers();
    header.append("Authorization", `Bearer ${token}`);
    header.append("Content-Type", "application/x-www-form-urlencoded");
    try {
      const res = await fetch(apiUrl.payment.initializePayment, {
        method: "POST",
        headers: header,
        body: formData.toString(),
      });

      const data = await res.json();
      console.log("Response:", data);

      // If backend returns a redirect/payment URL, redirect user
      if (data.redirect_url) {
        window.location.href = data.redirect_url;
      }
    } catch (err) {
      console.error("Error during payment initiation", err);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <form
        name="customerData"
        onSubmit={handleSubmit}
        className="bg-white p-8 rounded-xl shadow-lg w-full max-w-2xl"
      >
        <h2 className="text-2xl font-semibold text-center text-blue-600 mb-6">
          Checkout Page
        </h2>

        <div className="space-y-4">
          <FormRow label="order_id">
            <input
              type="text"
              name="order_id"
              value={form.order_id}
              readOnly
              className="w-full border px-3 py-2 rounded-md"
            />
          </FormRow>

          <FormRow label="amount">
            <input
              type="text"
              name="amount"
              value={form.amount}
              readOnly
              className="w-full border px-3 py-2 rounded-md"
            />
          </FormRow>

          <FormRow label="payment_page_client_id">
            <input
              type="text"
              name="payment_page_client_id"
              value={form.client_id}
              readOnly
              className="w-full border px-3 py-2 rounded-md"
            />
          </FormRow>

          <FormRow label="currency">
            <input
              type="text"
              name="currency"
              value="INR"
              readOnly
              className="w-full border px-3 py-2 rounded-md"
            />
          </FormRow>

          <FormRow label="return_url">
            <input
              type="text"
              name="redirect_url"
              value={`${url}/backend/handlePaymentResponse`}
              readOnly
              className="w-full border px-3 py-2 rounded-md"
            />
          </FormRow>

          <div className="text-right pt-4">
            <button
              type="submit"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition"
            >
              Pay Now
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
