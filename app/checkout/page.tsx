"use client";

import React, { useState, useEffect } from "react";
import { useFetch } from "@/utils/hooks/useFeatch";
import apiRoutes from "@/utils/api/routes";
import CartItems from "@/components/page/checkout/cartItems";
import EmptyCart from "@/components/presentation/emptyCart";
import Delivery from "@/components/page/checkout/delivery";
import { Icons } from "@/components/presentation/icons";
import { useRouter } from "next/navigation";
import { getCurrentSlot, calculateCartTotal } from "@/utils/helper";
import PaymentMethod from "@/components/page/checkout/payment";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import Image from "next/image";
import { toast } from "react-toastify";

function getTotal(data: any) {
  // Use the new helper function that includes variant and marination values
  return calculateCartTotal(data);
}

function Page() {
  const router = useRouter();
  const [isRealod, setIsRealod] = useState(false);
  const [token, setToken] = useState<string | undefined>(undefined);
  const [tokenChecked, setTokenChecked] = useState(false);
  const [isPlacing, setisPlacing] = useState(false);
  const [isOrderSuccess, setisOrderSuccess] = useState(false);
  const [isProceed, setIsProceed] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("upi");

  const [total, setTotal] = useState(0); // This will be the final payable amount
  const [subtotal, setSubtotal] = useState(0); // This will be the cart subtotal
  const [discount, setDiscount] = useState(0);
  const [platformFee, setPlatformFee] = useState(0);
  const [deliveryCharge, setDeliveryCharge] = useState(0);
  const [promoCode, setPromoCode] = useState<string>("");
  const [address, setAddress] = useState<string>("");
  const [timeslot, setTimeslot] = useState<string>("");

  const { data, isLoading } = useFetch<any[]>({
    url: apiRoutes.client.getCartList,
    token: token || undefined,
    start: !!token,
    dep: isRealod,
  });

  useEffect(() => {
    setSubtotal(getTotal(data));
  }, [data]);

  const { data: deliveryData, isLoading: deliveryTimeLoading } = useFetch<any>({
    url: apiRoutes.client.getDeliveryTime,
    start: true,
  });

  useEffect(() => {
    if (!deliveryTimeLoading && deliveryData?.length) {
      setPlatformFee(
        deliveryData.find((item: any) => item.key === "platform_fee")?.name,
      );
      const deliveryFee = deliveryData.find(
        (item: any) => item.key === "delivery_fee",
      )?.name;
      const freeDelivery = deliveryData.find(
        (item: any) => item.key === "free_delivery",
      )?.name;
      setDeliveryCharge(subtotal > freeDelivery ? 0 : deliveryFee);
      const slot = getCurrentSlot(deliveryData);
      setTimeslot(slot);
    }
  }, [deliveryTimeLoading, deliveryData, subtotal]);

  // Calculate final total whenever subtotal, platformFee, deliveryCharge, or discount changes
  useEffect(() => {
    const finalTotal =
      Number(subtotal) +
      Number(platformFee) +
      Number(deliveryCharge) -
      Number(discount);
    setTotal(finalTotal);
  }, [subtotal, platformFee, deliveryCharge, discount]);

  const placeOrder = async () => {
    try {
      setisPlacing(true);
      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      myHeaders.append("Authorization", `Bearer ${token}`);

      let backendPaymentMethod = 2;
      if (paymentMethod !== "upi") {
        backendPaymentMethod = 1;
      }

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({
          address,
          total,
          discount,
          promoCode,
          platformFee,
          deliveryCharge,
          paymentMethod: backendPaymentMethod,
        }),
      };
      const response = await fetch(apiRoutes.client.placeOrder, requestOptions);
      const result = await response.json();

      if (result?.status) {
        if (paymentMethod === "upi" && result?.data?.payment_url) {
          // For online payment, redirect to HDFC payment gateway
          window.location.href = result.data.payment_url;
        } else {
          // For COD, show success message and redirect to orders
          setisOrderSuccess(true);
          setTimeout(() => {
            router.push("/profile?tab=orders");
          }, 2000);
        }
      } else {
        toast.error(
          result?.message || "Failed to place order. Please try again.",
        );
      }
    } catch (error) {
      toast.error("Failed to place order. Please try again.");
    } finally {
      setisPlacing(false);
    }
  };

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
    setTokenChecked(true);
  }, []);

  useEffect(() => {
    if (!tokenChecked) return;

    if (!token) {
      router.push("/");
      return;
    }

    if (!isLoading && token && data && data.length === 0) {
      router.push("/");
    }
  }, [token, tokenChecked, data, isLoading, router]);

  const choosePayment = (data: string) => {
    setPaymentMethod(data);
  };

  return (
    <main className="w-full bg-light-50 min-h-screen">
      <div className="container py-5">
        <h1 className="text-[#555] text-[2rem] font-semibold mb-5">Checkout</h1>
        {!tokenChecked || (tokenChecked && token && isLoading) ? (
          <div className="flex justify-center items-center p-8">
            <Icons.loading className="w-8 h-8" />
          </div>
        ) : (
          <div
            className="grid grid-cols-12 gap-5"
            style={{ height: "calc(100vh - 180px)" }}
          >
            {isProceed ? (
              <PaymentMethod
                paymentMethod={paymentMethod}
                choosePayment={choosePayment}
              />
            ) : (
              <div className="col-span-7 bg-white shadow-md rounded-xl overflow-y-auto max-h-full">
                {data?.length ? (
                  <>
                    <CartItems
                      data={data}
                      isRealod={isRealod}
                      setIsRealod={setIsRealod}
                      token={token}
                    />
                    <div className="flex justify-center mb-5">
                      <button
                        onClick={() => router.push("/")}
                        className="bg-primary-200 p-1 rounded-lg text-white text-sm"
                      >
                        + Add more items
                      </button>
                    </div>
                  </>
                ) : (
                  <EmptyCart setIsOpen={() => {}} />
                )}
              </div>
            )}
            <div className="col-span-5 overflow-y-auto max-h-full">
              <Delivery
                token={token}
                total={subtotal}
                finalTotal={total}
                discount={discount}
                platformFee={platformFee}
                deliveryCharge={deliveryCharge}
                promoCode={promoCode}
                setPromoCode={setPromoCode}
                address={address}
                setAddress={setAddress}
                timeslot={timeslot}
                setTimeslot={setTimeslot}
                setDiscount={setDiscount}
                placeOrder={placeOrder}
                isPlacing={isPlacing}
                isProceed={isProceed}
                setIsProceed={setIsProceed}
              />
            </div>
          </div>
        )}
      </div>
      <Dialog open={isOrderSuccess}>
        <DialogContent>
          <div className=" text-center">
            <div className="flex justify-center items-center ">
              <Image
                src="/images/other/order_success.png"
                className="h-48  w-72 object-cover"
                width={500}
                height={500}
                alt=""
              />
            </div>

            <p className="text-center text-lg font-semibold text-dark-500 mt-4">
              Your Order Placed Successfully
            </p>
            <button
              onClick={() => router.push("/profile?tab=orders")}
              className="text-center text-sm font-normal text-[#3B66B1] hover:underline"
            >
              Track order
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </main>
  );
}

export default Page;
