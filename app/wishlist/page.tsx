"use client";

import React from "react";
import Wishlist from "@/components/page/user/wishList";
import { useWishlistStore } from "@/utils/store/wishlist";

function Page() {
  const wishlistStore = useWishlistStore();
  const itemCount = wishlistStore.getWishlistCount();

  return (
    <div className="container my-10">
      <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
        <h1 className="text-dark-50 font-semibold text-[1.4rem]">
          My Wishlist
        </h1>
        <div className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium">
          {itemCount} {itemCount === 1 ? "Item" : "Items"}
        </div>
      </div>

      <Wishlist />
    </div>
  );
}

export default Page;
