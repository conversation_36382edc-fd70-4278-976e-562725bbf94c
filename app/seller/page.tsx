import Image from "next/image";
import React from "react";
import apiUrl from "@/utils/api/routes";

async function getData() {
  try {
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");

    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: JSON.stringify({ slug: "seller-info" }),
    };

    const res = await fetch(apiUrl.server.getCms, requestOptions);
    const result = await res.json();
    const { data } = result;
    return data || {};
  } catch (error) {
    return {};
  }
}

interface PageProps {
  slug: string;
  sellerName: string;
  address: string;
  gst: string;
}

async function Page() {
  const data: PageProps = await getData();

  return (
    <div className="container my-10 min-h-[70vh]">
      <h1 className="text-dark-50 text-4xl font-semibold">
        Seller Information
      </h1>

      <div className="grid grid-cols-12 gap-5 mt-6">
        <div className="col-span-8">
          <div className="border border-red-500 w-full min-h-[20vh] rounded-2xl mt-5 p-14 pb-28">
            <div className="grid grid-cols-2 gap-y-14 mb-14">
              <div>
                <h2 className="text-base font-medium text-[#E03B3B]">
                  Seller Name
                </h2>
                <p className="text-dark-500 text-sm font-normal">
                  {data?.sellerName}
                </p>
              </div>

              <div>
                <h2 className="text-base font-medium text-[#E03B3B]">GSTN</h2>
                <p className="text-dark-500 text-sm font-normal">{data?.gst}</p>
              </div>

              <div>
                <h2 className="text-base font-medium text-[#E03B3B]">
                  Address
                </h2>
                <p className="text-dark-500 text-sm font-normal">
                  {data?.address}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-4">
          <Image
            width={500}
            height={500}
            alt="image"
            src="/images/other/shop.png"
          />
        </div>
      </div>
    </div>
  );
}

export default Page;
