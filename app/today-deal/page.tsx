"use client";

import ProductCard from "@/components/main/productCard";
import React, { useEffect, useState } from "react";
import apiUrl from "@/utils/api/routes";

async function Page() {
  const [data, setData] = useState<any>();

  useEffect(() => {
    async function getData() {
      try {
        const res = await fetch(apiUrl.client.dealOfTheDay, {
          method: "post",
          cache: "no-cache",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ limit: 100 }),
        });
        const result = await res.json();
        setData(result?.data);
        if (!res.ok || !result?.status) {
          throw new Error("Failed to fetch data");
        }
      } catch (error) {
        console.log(error);
      }
    }
    getData();
  }, []);

  return (
    <main className="w-full bg-[#FCFCFC]">
      <div className="container py-10">
        <h4 className="text-dark-50 text-4xl font-semibold">Today Deal</h4>
        <div className="mt-7 grid gap-3 grid-cols-5">
          {data?.data?.map((item: any, number: number) => (
            <ProductCard key={number} data={item} />
          ))}
        </div>
      </div>
    </main>
  );
}

export default Page;
