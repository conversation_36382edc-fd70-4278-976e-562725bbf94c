"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { Icons } from "@/components/presentation/icons";
import UserProfile from "@/components/page/user/profile";
import ProfileAddress from "@/components/page/user/address";
import Myorder from "@/components/page/user/myorder";
import Wishlist from "@/components/page/user/wishList";
import Wallet from "@/components/page/user/wallet";
import ReferAndEarn from "@/components/page/user/ReferAndEarn";
import PaymentSettings from "@/components/page/user/PaymentSettings";
import Contact from "@/components/page/user/contact";
import Cms from "@/components/page/user/Cms";
import Faq from "@/components/page/user/faq";
import Blogs from "@/components/page/user/blogs";
import News from "@/components/page/user/news";

import { useRouter } from "next/navigation";
import apiRoutes from "@/utils/api/routes";
import { useFetch } from "@/utils/hooks/useFeatch";
import { User } from "@/utils/types/user";
import { useSearchParams } from "next/navigation";
import { handleLogout as utilityLogout } from "@/utils/navigation";

function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedTab, setselectedTab] = useState(1);
  const [selectedCompanySubTab, setSelectedCompanySubTab] = useState<
    number | null
  >(null);
  const [isReload, setisReload] = useState(false);
  const [token, setToken] = useState<string | undefined>(undefined);
  const [companyOpen, setCompanyOpen] = useState(false);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
  }, []);

  // Handle URL parameters for tab selection
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab === "orders") {
      setselectedTab(3); // My Orders tab
    } else if (tab === "profile") {
      setselectedTab(1); // Edit Profile tab
    } else if (tab === "address") {
      setselectedTab(2); // Address tab
    } else if (tab === "wishlist") {
      setselectedTab(4); // Wishlist tab
    } else if (tab === "wallet") {
      setselectedTab(5); // Wallet tab
    }
  }, [searchParams]);

  const { data, isLoading } = useFetch<User>({
    url: apiRoutes.client.getUserInfo,
    token: token,
    start: !!token,
    dep: isReload,
  });

  // Tab index: 1=Edit Profile, 2=Address, 3=My Orders, 4=My Wishlist, 5=My Wallet, 6=Refer and Earn, 7=Payment Settings, 8=Contact Us, 9=Company
  // Company sub-tabs: 1=Terms, 2=Privacy Policy, 3=FAQ
  const tab = [
    { title: "Edit Proﬁle", icon: <Icons.editProfile /> },
    { title: "Address", icon: <Icons.address /> },
    { title: "My Orders", icon: <Icons.myCart /> },
    { title: "My Wishlist", icon: <Icons.wishlist1 /> },
    { title: "My Wallet", icon: <Icons.myWallet /> },
    { title: "Refer and Earn", icon: <Icons.refer /> },
    { title: "Payment Settings", icon: <Icons.payment /> },
    { title: "Contact Us", icon: <Icons.contact /> },
    { title: "Company", icon: <Icons.company /> },
    { title: "Blogs", icon: <Icons.blogs /> },
    { title: "News", icon: <Icons.news /> },
  ];
  const companySubTabs = [
    {
      title: "Terms & Conditions",
      component: <Cms slug="terms-and-condition" />,
    },
    { title: "Privacy Policy", component: <Cms slug="privacy-policy" /> },
    { title: "FAQs", component: <Faq page="profile" /> },
  ];

  const update = () => {
    setisReload(!isReload);
  };

  const handleLogout = () => {
    utilityLogout(router);
  };

  return (
    <main className="w-full bg-light-50 min-h-[80vh]">
      <div className="container py-10">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-3">
            <div className="block  rounded-lg shadow">
              <div className="bg-gradient-to-r from-[#3092D0] to-[#59C4C2] rounded-t-lg p-7">
                <div className="flex gap-2">
                  <Image
                    className="w-16 h-16 rounded-full aspect-auto"
                    src={data?.avatar || "/images/people/profile.png"}
                    width={100}
                    height={100}
                    alt="Rounded avatar"
                  />
                  <div>
                    <h6 className="text-sm font-light text-white">Hello,</h6>
                    <h2 className="text-lg font-semibold text-white">
                      {data?.firstName} {data?.lastName}
                    </h2>
                    <p className="font-normal text-[0.65rem] text-white">
                      {data?.mobileNo}
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <div className=" text-gray-900 bg-white border border-gray-200 rounded-b-lg ">
                  {tab.map((item, index) =>
                    item.title !== "Company" ? (
                      <button
                        key={index}
                        onClick={() => {
                          setselectedTab(index + 1);
                          setSelectedCompanySubTab(null);
                        }}
                        type="button"
                        className={`inline-flex gap-5 items-center w-full px-4 py-3 border-b focus:z-10 ${selectedTab == index + 1 ? "bg-[#FFF4F4] text-sm font-semibold text-dark-50" : "bg-white text-sm font-normal text-dark-500"}`}
                      >
                        {item.icon}
                        {item.title}
                      </button>
                    ) : (
                      <div key={index}>
                        <button
                          onClick={() => {
                            setselectedTab(index + 1);
                            setCompanyOpen((prev) => !prev);
                          }}
                          type="button"
                          className={`inline-flex gap-5 items-center w-full px-4 py-3 border-b focus:z-10 ${selectedTab == index + 1 ? "bg-[#FFF4F4] text-sm font-semibold text-dark-50" : "bg-white text-sm font-normal text-dark-500"}`}
                        >
                          {item.icon}
                          {item.title}
                          <span className="ml-auto">
                            {companyOpen ? "▲" : "▼"}
                          </span>
                        </button>
                        {companyOpen && selectedTab === index + 1 && (
                          <div className="ml-8">
                            {companySubTabs.map((sub, subIdx) => (
                              <button
                                key={subIdx}
                                onClick={() => setSelectedCompanySubTab(subIdx)}
                                type="button"
                                className={`block w-full text-left px-4 py-2 border-b focus:z-10 ${selectedCompanySubTab === subIdx ? "bg-[#FFF4F4] text-dark-50 font-semibold" : "bg-white text-dark-500 font-normal"}`}
                              >
                                {sub.title}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    ),
                  )}
                  <button
                    type="button"
                    onClick={handleLogout}
                    className={`inline-flex gap-5 items-center w-full px-4 py-3 border-b focus:z-10 bg-white text-sm font-normal text-dark-500`}
                  >
                    <Icons.logout />
                    Logout
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="col-span-9">
            <div className="block bg-white border border-gray-200 rounded-lg shadow">
              <div className="text-dark-50 font-semibold text-[1.4rem] my-4 mx-6 flex items-center">
                {selectedTab === 9 && selectedCompanySubTab !== null
                  ? companySubTabs[selectedCompanySubTab].title
                  : tab[selectedTab - 1]?.title}
              </div>
              <hr className="w-full h-[1px] bg-[#E9E9E9] " />
              <div className="m-5">
                {selectedTab == 1 && (
                  <UserProfile
                    data={data}
                    token={token || ""}
                    update={update}
                  />
                )}
                {selectedTab == 2 && <ProfileAddress token={token || ""} />}
                {selectedTab == 3 && <Myorder token={token || ""} />}
                {selectedTab == 4 && <Wishlist />}
                {selectedTab == 5 && <Wallet />}
                {selectedTab == 6 && <ReferAndEarn />}
                {selectedTab == 7 && <PaymentSettings />}
                {selectedTab == 8 && <Contact />}
                {selectedTab == 9 && selectedCompanySubTab === 0 && (
                  <Cms slug="terms-and-condition" />
                )}
                {selectedTab == 9 && selectedCompanySubTab === 1 && (
                  <Cms slug="privacy-policy" />
                )}
                {selectedTab == 9 && selectedCompanySubTab === 2 && (
                  <Faq page="profile" />
                )}
                {selectedTab == 10 && <Blogs />}
                {selectedTab == 11 && <News />}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export default Page;
