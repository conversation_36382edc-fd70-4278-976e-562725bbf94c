"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { useFetch } from "@/utils/hooks/useFeatch";
import { AddToCartParams, Product } from "@/utils/types/product";
import Nodata from "@/components/presentation/noData";
import apiRoutes from "@/utils/api/routes";
import { Icons } from "@/components/presentation/icons";
import { useWishlistStore } from "@/utils/store/wishlist";
import QuantityControl from "@/components/main/quantityControl";
import { useCartStore } from "@/utils/store/cart";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { getCartItem, hasVariantsOrMarination, toFloat } from "@/utils/helper";
import Variantcard from "@/components/main/variantCard";
import MarinationCard from "@/components/main/marinationCard";

function Page({ params }: { params: { slug: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  const [copied, setCopied] = useState(false);
  const [shareUrl, setShareUrl] = useState("");

  const cartlistState = useCartStore();
  const wishlistState = useWishlistStore();
  const [selectedVariant, setSelectedVariant] = useState<string | null>(null);

  const { data, isLoading } = useFetch<Product>({
    url: apiRoutes.client.getProduct,
    start: true,
    options: { body: JSON.stringify({ id: params.slug }) },
  });

  const [token, setToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
    setShareUrl(window.location.href);
  }, []);

  useEffect(() => {
    if (
      data?.marinationTypes &&
      data.marinationTypes.length > 0 &&
      data?.variants &&
      data.variants.filter((item: any) => item.status === "Active").length > 0
    ) {
      setSelectedVariant(
        data.variants.filter((item: any) => item.status === "Active")[0]?.id
          ?._id,
      );
    }
  }, [data]);

  const updateWishList = async () => {
    const id = data?.id || data?._id;
    try {
      try {
        const headers = new Headers();
        headers.append("Content-Type", "application/json");
        headers.append("Authorization", `Bearer ${token}`);
        const response = await fetch(apiRoutes.client.addWishList, {
          method: "POST",
          body: JSON.stringify({ id }),
          headers: headers,
        });
        const result = await response.json();
        if (result?.status) {
          wishlistState.setWishList(result?.data || []);
        }
      } catch (error) {
        console.log(error);
      } finally {
      }
    } catch (error) {}
  };

  const addToCart = async ({
    productId,
    variantId,
    marinationId,
  }: AddToCartParams) => {
    if (!token) {
      router.push("/login");
    } else {
      try {
        const headers = new Headers();
        headers.append("Content-Type", "application/json");
        headers.append("Authorization", `Bearer ${token}`);

        const body: Record<string, any> = {
          id: productId,
          quantity: 1,
        };

        if (variantId) {
          body.variant = variantId;
        }

        if (marinationId) {
          body.marinationType = marinationId;

          if (selectedVariant) {
            body.variant = selectedVariant;
          }
        }

        const response = await fetch(apiRoutes.client.addToCart, {
          method: "POST",
          body: JSON.stringify(body),
          headers: headers,
        });
        const result = await response.json();
        if (result?.status) {
          toast({ title: "Product added to cart" });
          cartlistState.setCartList(result?.data);
        }
      } catch (error) {
        console.log(error);
        toast({ content: "Failed to add product card" });
      }
    }
  };

  const handleCopy = async () => {
    if (!shareUrl) return;
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 32000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  if (isLoading)
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <div role="status">
            <svg
              aria-hidden="true"
              className="inline w-8 h-8 text-gray-200 animate-spin fill-red-60"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      </div>
    );
  if (!data?._id) return <Nodata />;

  return (
    <main className="w-full bg-white">
      <div className="container py-10">
        <div className="grid grid-cols-2 gap-20 ">
          <div>
            <div className="relative">
              <Image
                src={
                  data?.images?.[0] || "/images/product/fish_product_image.jpg"
                }
                className="rounded-xl object-cover items-end h-80"
                alt="product"
                width={700}
                height={344}
                priority
              />
              <div className="absolute bottom-3 left-3">
                <Icons.veg isVeg={data?.isVeg} />
              </div>
            </div>

            <p className="mt-7 text-[#343434] text-base font-light">
              {data?.description}
            </p>

            <div className="mt-9 flex rounded-xl items-center bg-[#CDDEE9] p-6 gap-5">
              <div className="flex-shrink-0">
                <Icons.storage />
              </div>

              <div>
                <h3 className="text-[#3D3D3D] text-base font-semibold">
                  Storage Instructions:
                </h3>

                <p className="text-xs font-light text-[#3D3D3D] mt-1">
                  {data?.storageInstruction}
                </p>
              </div>
            </div>
          </div>

          <div className="col-sapn-1">
            <h1 className="text-[#232323] font-semibold text-[28px] line-clamp-2 mb-3">
              {`${data?.title} | ${data?.weight}`}
            </h1>
            <p className="text-dark-450 text-base">
              {data?.category?.title || ""}
            </p>
            <span className="bg-[#E2E2E2] py-0.5 inline-flex items-center rounded px-3 text-base font-normal mt-3">
              <Icons.greenStar />
              <span className="ms-2">
                {data?.averageRating} | {data?.totalReviews}
              </span>
              <span className="ms-3 text-[#898989] text-base">(Ratings)</span>
            </span>

            <div className="mt-4">
              <span className="text-[#3D3D3D] text-base">
                Price based on Gross Weight before Cleaning/Cutting
              </span>
            </div>

            {data?.stock?.stock !== 0 ? (
              <div className=" mt-3 space-x-5">
                {data?.discount ? (
                  <>
                    <span className="text-[#898989] font-normal text-[21px] line-through">
                      ₹ {toFloat(data?.mrp)}
                    </span>
                    <span>
                      <span className="text-[#333333] text-[28px] font-semibold">
                        ₹{toFloat(data?.offerPrice)}{" "}
                      </span>
                      <span className="text-[#6C6B6B] font-normal text-[21px]">
                        /{data?.weight}{" "}
                      </span>
                    </span>
                  </>
                ) : (
                  <>
                    <span className="text-[#333333] text-[28px] font-semibold">
                      ₹{toFloat(data?.mrp)}
                    </span>
                    <span className="text-[#6C6B6B] font-normal text-[21px]">
                      /{data?.weight}{" "}
                    </span>
                  </>
                )}
                {data?.discount && (
                  <span className="px-5 py-0.5 bg-[#a6dfdd] text-[#3D3D3D] text-[21px] font-normal">
                    {data?.discount}% Off
                  </span>
                )}
              </div>
            ) : (
              <p className="text-[#FD7878] font-semibold text-[28px]">
                Item out of Stock
              </p>
            )}

            <div className="mt-5 flex gap-10 items-end">
              <button
                type="button"
                onClick={updateWishList}
                className="w-30 min-h-[42px] text-[#898989] bg-[#EAEAEA] rounded-lg text-xs font-semibold px-5 py-2.5 text-center inline-flex items-center gap-3"
              >
                <Icons.wishList
                  className={
                    wishlistState.isInWishlist(data?.id || data?._id)
                      ? "fill-red-50"
                      : "fill-dark-200"
                  }
                />
                Wishlist
              </button>
              <div className="relative inline-block">
                <button
                  type="button"
                  onClick={handleCopy}
                  className="w-30 min-h-[42px] text-[#898989] bg-[#EAEAEA] rounded-lg text-xs font-semibold px-5 py-2.5 text-center inline-flex items-center gap-3"
                >
                  <Icons.shareArrow />
                  Share
                </button>
                {copied && (
                  <span className="absolute text-xs text-primary-100 font-medium bg-white px-2 py-1 rounded shadow">
                    Link copied!
                  </span>
                )}
              </div>
            </div>

            {data?.stock?.stock !== 0 ? (
              <div className="mt-5">
                {(() => {
                  const cartItem = getCartItem({
                    productId: data?.id,
                    cartlistState,
                    dataId: data?.id || data?._id,
                  });
                  const hasVariantsOrMar = hasVariantsOrMarination(data);

                  if (!hasVariantsOrMar) {
                    if (cartItem) {
                      return (
                        <QuantityControl
                          cartItem={
                            getCartItem({
                              productId: data?.id,
                              cartlistState,
                              dataId: data?.id || data?._id,
                            }) || {}
                          }
                          className="bg-white border rounded-lg px-2 py-1"
                        />
                      );
                    } else {
                      return (
                        <button
                          className="flex items-center gap-2 p-2 rounded-md min-w-[114px] bg-primary-100 hover:bg-blue-700 text-white shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-75 text-xs"
                          onClick={() =>
                            addToCart({ productId: data?.id || data?._id })
                          }
                        >
                          <Icons.bag />
                          Add to Bag
                        </button>
                      );
                    }
                  } else {
                    return null;
                  }
                })()}

                {/* variants */}
                <Variantcard
                  data={data}
                  selectedVariant={selectedVariant}
                  setSelectedVariant={setSelectedVariant}
                  cartlistState={cartlistState}
                  addToCart={addToCart}
                />

                {/* marination type */}
                <MarinationCard
                  data={data}
                  selectedVariant={selectedVariant}
                  cartlistState={cartlistState}
                  addToCart={addToCart}
                />
              </div>
            ) : (
              <div className="mt-5">
                <button className="flex items-center gap-2 p-2 rounded-md min-w-[114px] bg-primary-100 hover:bg-blue-700 text-white shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-75 text-xs">
                  <Icons.notify />
                  Notify me
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}

export default Page;
