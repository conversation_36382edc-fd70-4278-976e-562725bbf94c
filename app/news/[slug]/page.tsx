import React from "react";
import Image from "next/image";
import getData from "@/utils/api/getData";
import apiUrl from "@/utils/api/routes";
import { Blog } from "@/utils/types/common";

async function NewsDetails({ params }: { params: { slug: string } }) {
  const data: Blog = await getData({
    url: apiUrl.server.getNews,
    body: { newsId: params?.slug },
  });
  const latestNews: Blog[] = await getData({
    url: apiUrl.server.getLatestNews,
  });

  return (
    <main className="w-full bg-white">
      <div className="container py-10">
        <h1 className="font-semibold text-dark-300 text-4xl">News</h1>
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-9 border-r-2 pe-5">
            <div className="mt-3">
              <span className="text-sm font-normal text-dark-450">
                {new Date(data?.createdAt)?.toLocaleDateString()}
              </span>
              <Image
                src={data?.image || "/images/other/news2.png"}
                className="object-contain items-end my-2 w-full"
                alt="product"
                width={1000}
                height={400}
              />
              <h1 className="text-2xl font-semibold text-dark-50 mt-4">
                {data?.title}{" "}
              </h1>
              <div
                className="text-sm font-normal text-dark-500 mt-3"
                dangerouslySetInnerHTML={{ __html: data?.desctiption || "" }}
              />
            </div>
          </div>
          <div className="col-span-3">
            <h5 className="text-lg font-medium text-dark-50">RECENT POSTS</h5>
            <hr className="h-px mb-7 bg-[#939393] border-0" />

            {latestNews?.map((item, index) => (
              <div key={index}>
                <h6 className="text-sm font-semibold text-dark-50 mt-1">
                  {item?.title}.
                </h6>
                <p className="text-sm font-normal mt-2 line-clamp-4">
                  {item?.shortDescription}
                </p>
                <hr className="h-1 my-5 text-[#cecece]" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}

export default NewsDetails;
