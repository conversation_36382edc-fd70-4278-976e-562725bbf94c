import React from "react";
import NewsCard from "@/components/main/newsCard";
import Image from "next/image";
import getData from "@/utils/api/getData";
import { Blog } from "@/utils/types/common";
import apiUrl from "@/utils/api/routes";
import Link from "next/link";

async function News() {
  const [latestNews, mostRead]: any[] = await Promise.allSettled([
    await getData({ url: apiUrl.server.getLatestNews }),
    await getData({ url: apiUrl.server.getLatestNews }),
  ]);

  return (
    <main className="w-full bg-light-50">
      <div className="container py-10">
        <h1 className="font-semibold text-dark-300 text-4xl">News</h1>

        <div className="mt-14">
          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-5 border-r-2">
              {latestNews?.value?.map((item: Blog, index: number) => (
                <div key={index}>
                  <Link href={`/news/${item?.id}`}>
                    <NewsCard item={item} />
                  </Link>
                  <hr className="h-1 my-5 text-[#cecece] " />
                </div>
              ))}
            </div>

            <div className="col-span-4 border-r-2">
              <h5 className="text-lg font-medium text-dark-50">LATEST NEWS</h5>
              {latestNews?.value?.map((item: Blog, index: number) => (
                <div key={null} className="w-4/5">
                  <Link href={`/news/${item?.id}`}>
                    <div className="mt-3">
                      <span className="text-sm font-normal text-dark-450">
                        {new Date(item?.createdAt)?.toLocaleDateString(
                          "en-GB",
                          { day: "2-digit", month: "long", year: "numeric" },
                        )}
                        , Delhi
                      </span>
                    </div>
                    <h6 className="text-sm font-semibold text-dark-50 mt-1">
                      {item?.title}.
                    </h6>
                    <p className="text-sm font-normal mt-2 line-clamp-4">
                      {item?.shortDescription}
                    </p>
                  </Link>
                  <hr className="h-1 my-5 text-[#cecece]" />
                </div>
              ))}
            </div>

            <div className="col-span-3">
              <h5 className="text-lg font-medium text-dark-50">MOST READ</h5>
              {mostRead?.value?.map((item: Blog, index: number) => (
                <div key={index}>
                  <Link href={`/news/${item?.id}`}>
                    <div className="mt-3">
                      <span className="text-sm font-normal text-dark-450">
                        {new Date(item?.createdAt)?.toLocaleDateString(
                          "en-GB",
                          { day: "2-digit", month: "long", year: "numeric" },
                        )}
                      </span>
                    </div>
                    <Image
                      src={item?.image || "/images/other/news1.png"}
                      className="object-contain items-end my-1"
                      alt="product"
                      width={516}
                      height={344}
                    />
                    <h6 className="text-sm font-semibold text-dark-50 mt-1">
                      {item?.title}
                    </h6>
                    <p className="text-sm font-normal mt-2 line-clamp-4">
                      {item?.shortDescription}
                    </p>
                    <button className="text-xs font-semibold">Read more</button>
                  </Link>
                  <hr className="h-1 my-5 text-[#cecece]" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

export default News;
