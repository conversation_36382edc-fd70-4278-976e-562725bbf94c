import Image from "next/image";
import Categories from "@/components/page/home/<USER>";
import Products from "@/components/page/home/<USER>";
import Banner from "@/components/page/home/<USER>";
import apiUrl from "@/utils/api/routes";
import FlashSales from "@/components/page/home/<USER>";
import Enquiry from "@/components/page/home/<USER>";
import Video from "@/components/page/home/<USER>";
import Highlite from "@/components/page/home/<USER>";

export default function Home() {
  return (
    <main className="w-full bg-light-50">
      <div className="container py-10">
        <Banner />
        <Highlite />
        <FlashSales
          title="Flash Sale"
          url={apiUrl.client.getFlashProduct}
          timer="05:23:12"
          subTitle="Hurry Up !"
          page="flash-sales"
        />
        <Products
          title="Today’s Deal"
          url={apiUrl.client.dealOfTheDay}
          page="today-deal"
        />
        <Categories />
        <Products
          title="Newly Arrived"
          url={apiUrl.client.getNewlyArrived}
          page="new-arrivel"
        />
        <Products
          title="Trending"
          url={apiUrl.client.getTrendingProduct}
          page="trending"
        />
        {/* <Products
          title="Combo Packs"
          url={apiUrl.client.getCompoProduct}
          page="combo"
        /> */}

        <div className="mt-5 grid grid-cols-2 gap-5">
          <div>
            <Image
              src="/images/offer/offer1.png"
              width={650}
              height={325}
              alt="offer"
              className="w-100"
            />
          </div>

          <div>
            <Image
              src="/images/offer/offer1.png"
              width={650}
              height={325}
              alt="offer"
              className="object-cover aspect-auto"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 mt-5 w-full rounded-xl">
          <Video />
        </div>

        <div className="grid grid-cols-12 mt-5 gap-5">
          <div className="col-span-7 bg-[#3F8DCC] text-white p-10 rounded-2xl flex justify-between">
            <div className="flex flex-col justify-end w-1/2">
              <div className="">
                <h6 className="text-2xl text-white font-medium ">WE LIKE</h6>
                <h6 className="text-2xl text-white font-medium ">
                  SHOWING OFF
                </h6>
                <h6 className="text-2xl text-white font-medium ">
                  OUR CERTIFICATES
                </h6>
              </div>
            </div>
            <div className="w-1/2 flex justify-end">
              <Image
                src="/images/other/cartificate.png"
                className="object-contain aspect-auto"
                width={200}
                height={100}
                alt="image"
              />
            </div>
          </div>
          <Enquiry isShowTitle={true} />
        </div>
      </div>
    </main>
  );
}
