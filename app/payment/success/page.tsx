"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Icons } from "@/components/presentation/icons";

export default function PaymentSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("orderId");
  const amount = searchParams.get("amount");
  const hdfcOrderId = searchParams.get("hdfcOrderId");
  const timestamp = searchParams.get("timestamp");
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          router.push("/profile?tab=orders");
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  return (
    <main className="w-full bg-light-50 min-h-screen flex items-center justify-center">
      <div className="container max-w-xl mx-auto">
        <div className="bg-white shadow-lg rounded-xl p-8 text-center">
          {/* Success Icon */}
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Icons.greenCheck className="w-10 h-10 text-green-600" />
          </div>

          {/* Success Message */}
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Payment Successful!
          </h1>

          <p className="text-gray-600 mb-6">
            Your payment has been processed successfully. Your order has been
            confirmed.
          </p>

          {/* Payment Details */}
          <div className="space-y-4 mb-6">
            {orderId && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-500 mb-1">Order ID</p>
                <p className="font-mono text-lg font-semibold text-gray-800">
                  {orderId}
                </p>
              </div>
            )}

            {hdfcOrderId && (
              <div className="bg-blue-50 rounded-lg p-4">
                <p className="text-sm text-blue-600 mb-1">
                  HDFC Transaction ID
                </p>
                <p className="font-mono text-sm font-semibold text-blue-800">
                  {hdfcOrderId}
                </p>
              </div>
            )}

            {amount && (
              <div className="bg-green-50 rounded-lg p-4">
                <p className="text-sm text-green-600 mb-1">Amount Paid</p>
                <p className="font-mono text-xl font-bold text-green-800">
                  ₹{parseFloat(amount).toFixed(2)}
                </p>
              </div>
            )}

            {timestamp && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-500 mb-1">Payment Time</p>
                <p className="text-sm font-medium text-gray-800">
                  {new Date(parseInt(timestamp)).toLocaleString("en-IN", {
                    dateStyle: "medium",
                    timeStyle: "medium",
                  })}
                </p>
              </div>
            )}
          </div>

          {/* Countdown */}
          <p className="text-sm text-gray-500 mb-6">
            Redirecting to your orders in {countdown} seconds...
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => router.push("/profile?tab=orders")}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              View My Orders
            </button>

            <button
              onClick={() => router.push("/")}
              className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              Continue Shopping
            </button>
          </div>

          {/* Additional Info */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              You will receive an email confirmation shortly with your order
              details.
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
