"use client";

import React, { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Icons } from "@/components/presentation/icons";

export default function PaymentFailedPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("orderId");
  const amount = searchParams.get("amount");
  const hdfcOrderId = searchParams.get("hdfcOrderId");
  const timestamp = searchParams.get("timestamp");
  const [isRetrying, setIsRetrying] = useState(false);

  const retryPayment = async () => {
    if (!orderId) {
      alert("Order ID not found. Please try placing the order again.");
      router.push("/checkout");
      return;
    }

    try {
      setIsRetrying(true);
      const token = localStorage.getItem("accessToken");

      if (!token) {
        router.push("/");
        return;
      }

      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      myHeaders.append("Authorization", `Bearer ${token}`);

      const response = await fetch("/backend/payments/retry", {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({ id: orderId }),
      });

      const result = await response.json();

      if (result?.status && result?.data?.payment?.payment_links?.web) {
        // Redirect to payment gateway
        window.location.href = result.data.payment.payment_links.web;
      } else {
        alert("Failed to retry payment. Please try again.");
      }
    } catch (error) {
      console.error("Retry payment error:", error);
      alert("Failed to retry payment. Please try again.");
    } finally {
      setIsRetrying(false);
    }
  };

  return (
    <main className="w-full bg-light-50 min-h-screen flex items-center justify-center">
      <div className="container max-w-xl mx-auto">
        <div className="bg-white shadow-lg rounded-xl p-8 text-center">
          {/* Failed Icon */}
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Icons.x className="w-10 h-10 text-red-600" />
          </div>

          {/* Failed Message */}
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Payment Failed
          </h1>

          <p className="text-gray-600 mb-6">
            We couldn't process your payment. Your order is still pending.
            Please try again or choose a different payment method.
          </p>

          {/* Payment Details */}
          <div className="space-y-4 mb-6">
            {orderId && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-500 mb-1">Order ID</p>
                <p className="font-mono text-lg font-semibold text-gray-800">
                  {orderId}
                </p>
              </div>
            )}

            {hdfcOrderId && (
              <div className="bg-red-100 rounded-lg p-4">
                <p className="text-sm text-red-600 mb-1">HDFC Transaction ID</p>
                <p className="font-mono text-sm font-semibold text-red-800">
                  {hdfcOrderId}
                </p>
              </div>
            )}

            {amount && (
              <div className="bg-yellow-50 rounded-lg p-4">
                <p className="text-sm text-yellow-600 mb-1">Amount (Failed)</p>
                <p className="font-mono text-xl font-bold text-yellow-800">
                  ₹{parseFloat(amount).toFixed(2)}
                </p>
              </div>
            )}

            {timestamp && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-500 mb-1">Attempt Time</p>
                <p className="text-sm font-medium text-gray-800">
                  {new Date(parseInt(timestamp)).toLocaleString("en-IN", {
                    dateStyle: "medium",
                    timeStyle: "medium",
                  })}
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {orderId && (
              <button
                onClick={retryPayment}
                disabled={isRetrying}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isRetrying ? (
                  <>
                    <Icons.loading className="w-5 h-5 mr-2" />
                    Retrying...
                  </>
                ) : (
                  "Retry Payment"
                )}
              </button>
            )}

            <button
              onClick={() => router.push("/checkout")}
              className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              Try Different Payment Method
            </button>

            <button
              onClick={() => router.push("/profile?tab=orders")}
              className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              View My Orders
            </button>
          </div>

          {/* Additional Info */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              If you continue to experience issues, please contact our support
              team.
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
