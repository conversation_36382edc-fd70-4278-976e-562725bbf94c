"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Icons } from "@/components/presentation/icons";

export default function PaymentPendingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("orderId");
  const amount = searchParams.get("amount");
  const hdfcOrderId = searchParams.get("hdfcOrderId");
  const timestamp = searchParams.get("timestamp");
  const [countdown, setCountdown] = useState(10);
  const [isChecking, setIsChecking] = useState(false);

  const checkPaymentStatus = async () => {
    if (!orderId) {
      router.push("/checkout");
      return;
    }

    try {
      setIsChecking(true);
      const token = localStorage.getItem("accessToken");

      if (!token) {
        router.push("/");
        return;
      }

      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      myHeaders.append("Authorization", `Bearer ${token}`);

      const response = await fetch("/backend/payments/verify", {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({ orderId }),
      });

      const result = await response.json();

      if (result?.status) {
        const paymentStatus = result.data?.paymentStatus;

        if (paymentStatus === "SUCCESS") {
          router.push(`/payment/success?orderId=${orderId}`);
        } else if (paymentStatus === "FAILED") {
          router.push(`/payment/failed?orderId=${orderId}`);
        }
        // If still pending, continue waiting
      }
    } catch (error) {
      console.error("Payment status check error:", error);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    // Check payment status every 3 seconds
    const statusInterval = setInterval(checkPaymentStatus, 3000);

    // Countdown timer
    const countdownTimer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(countdownTimer);
          clearInterval(statusInterval);
          router.push("/profile?tab=orders");
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(statusInterval);
      clearInterval(countdownTimer);
    };
  }, [router, orderId]);

  return (
    <main className="w-full bg-light-50 min-h-screen flex items-center justify-center">
      <div className="container max-w-xl mx-auto">
        <div className="bg-white shadow-lg rounded-xl p-8 text-center">
          {/* Pending Icon */}
          <div className="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Icons.loading className="w-10 h-10 text-yellow-600 animate-spin" />
          </div>

          {/* Pending Message */}
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Payment Processing
          </h1>

          <p className="text-gray-600 mb-6">
            Your payment is being processed. Please wait while we confirm your
            transaction.
          </p>

          {/* Payment Details */}
          <div className="space-y-4 mb-6">
            {orderId && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-500 mb-1">Order ID</p>
                <p className="font-mono text-lg font-semibold text-gray-800">
                  {orderId}
                </p>
              </div>
            )}

            {hdfcOrderId && (
              <div className="bg-yellow-50 rounded-lg p-4">
                <p className="text-sm text-yellow-600 mb-1">
                  HDFC Transaction ID
                </p>
                <p className="font-mono text-sm font-semibold text-yellow-800">
                  {hdfcOrderId}
                </p>
              </div>
            )}

            {amount && (
              <div className="bg-blue-50 rounded-lg p-4">
                <p className="text-sm text-blue-600 mb-1">Amount Processing</p>
                <p className="font-mono text-xl font-bold text-blue-800">
                  ₹{parseFloat(amount).toFixed(2)}
                </p>
              </div>
            )}

            {timestamp && (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-500 mb-1">Started At</p>
                <p className="text-sm font-medium text-gray-800">
                  {new Date(parseInt(timestamp)).toLocaleString("en-IN", {
                    dateStyle: "medium",
                    timeStyle: "medium",
                  })}
                </p>
              </div>
            )}
          </div>

          {/* Status Check */}
          <div className="mb-6">
            {isChecking ? (
              <p className="text-sm text-blue-600 flex items-center justify-center">
                <Icons.loading className="w-4 h-4 mr-2 animate-spin" />
                Checking payment status...
              </p>
            ) : (
              <p className="text-sm text-gray-500">
                Automatically checking payment status...
              </p>
            )}
          </div>

          {/* Countdown */}
          <p className="text-sm text-gray-500 mb-6">
            Redirecting to orders in {countdown} seconds if no update...
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={checkPaymentStatus}
              disabled={isChecking}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isChecking ? "Checking..." : "Check Status Now"}
            </button>

            <button
              onClick={() => router.push("/profile?tab=orders")}
              className="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              View My Orders
            </button>
          </div>

          {/* Additional Info */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              Please do not close this page or refresh your browser while the
              payment is being processed.
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
