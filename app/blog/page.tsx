"use client";

import React, { useState, useEffect } from "react";
import BlogCard from "@/components/main/blogCard";
import apiUrl from "@/utils/api/routes";
import { Blog } from "@/utils/types/common";
import getData from "@/utils/api/getData";
import { useFetch } from "@/utils/hooks/useFeatch";
import CardPlaceHolder from "@/components/presentation/cardPlaceholder";
import Nodata from "@/components/presentation/noData";

function Page() {
  // const data: Blog[] = await getData({ url: apiUrl.server.getBlogs, body: { limit: 100 } })

  const [filter, setfilter] = useState("latest");

  // const { data, isLoading, error } = useFetch<Blog[]>(apiUrl.client.getBlogs, { body: JSON.stringify({ data: filter }) }, { initialData: [], dep: filter })
  // const { data, isLoading, error } = useFetch<Blog[]>({
  //   url: apiUrl.client.getBlogs,
  //   options: { body: JSON.stringify({ data: filter }) },
  //   initialData: [],
  //   dep: filter,
  // });

  const [data, setData] = useState<any>();
  useEffect(() => {
    async function getData() {
      try {
        const res = await fetch(apiUrl.client.getBlogs, {
          method: "post",
          cache: "no-cache",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ limit: 100 }),
        });
        const result = await res.json();
        setData(result);
        if (!res.ok || !result?.status) {
          // This will activate the closest `error.js` Error Boundary
          throw new Error("Failed to fetch data");
        }
      } catch (error) {
        console.log(error);
      }
    }
    getData();
  }, []);

  return (
    <main className="w-full bg-[#fff]">
      <div className="container py-10">
        <div className="flex justify-between">
          <h1 className="font-semibold text-dark-300 text-5xl">Blogs</h1>
          {!!data?.data?.length && (
            <select
              className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg  block  p-2.5"
              onChange={(e) => setfilter(e.target.value)}
            >
              <option defaultValue="latest">Latest</option>
              <option value="most-read">Most Read</option>
            </select>
          )}
        </div>

        <div className="grid grid-cols-3 mt-14 gap-11">
          {data?.data?.data?.map((item: any, index: number) => (
            <div key={index}>
              {" "}
              <BlogCard item={item} />{" "}
            </div>
          ))}
        </div>

        {/* {isLoading && (
          <div className="grid grid-cols-3  gap-11">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <div key={item}>
                <CardPlaceHolder />{" "}
              </div>
            ))}
          </div>
        )}

        {!isLoading && !data?.length && <Nodata />} */}
      </div>
    </main>
  );
}

// async function getData() {
//     const res = await fetch(apiUrl.server.getBlogs, {
//       method: 'post',
//       cache: "no-cache",
//       headers: {
//         'Content-Type': 'application/json'
//       },
//       body: JSON.stringify({ limit: 100 })
//     })
//     const result = await res.json()
//     if (!res.ok || !result?.status) {
//       // This will activate the closest `error.js` Error Boundary
//       throw new Error('Failed to fetch data')
//     }
//     const { data } = result
//     return data
//   }

export default Page;
