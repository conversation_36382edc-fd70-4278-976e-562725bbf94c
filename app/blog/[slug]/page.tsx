import React from "react";
import Image from "next/image";
import apiUrl from "@/utils/api/routes";
import { Blog } from "@/utils/types/common";
import getData from "@/utils/api/getData";

async function DetailsPage({ params }: { params: { slug: string } }) {
  const data: Blog = await getData({
    url: apiUrl.server.getBlog,
    body: { blogId: params?.slug },
  });

  return (
    <main className="w-full bg-white">
      <div className="container py-10">
        <div className="flex justify-between">
          <h1 className="font-semibold text-dark-300 text-5xl">Blogs</h1>
          {/* <div className="flex items-center gap-3">
                        <button type="button" className=" text-red-50 border-red-50 border hover:bg-red-50 text-sm font-medium px-5 py-2.5 text-center inline-flex items-center font-inter">
                            PREV BLOG
                        </button>
                        <button type="button" className="text-white bg-red-50 hover:bg-red-50 text-sm font-medium px-5 py-2.5 text-center inline-flex items-center font-inter">
                            NEXT BLOG
                            <svg className="w-3.5 h-3.5 ml-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M1 5h12m0 0L9 1m4 4L9 9" />
                            </svg>
                        </button>
                    </div> */}
        </div>

        <div className="grid grid-cols-2 mt-10 justify-end gap-14">
          <div>
            <Image
              src={data?.image || "/images/product/product4.png"}
              className="rounded-2xl object-cover items-end w-full"
              alt="product"
              width={516}
              height={344}
            />
            <Image
              src={data?.image || "/images/product/product4.png"}
              className="rounded-2xl object-cover items-end mt-5 w-full"
              alt="product"
              width={516}
              height={344}
            />
          </div>

          <div>
            <div className="flex justify-between items-center mt-2">
              <div className="flex items-center space-x-4 mt-2">
                <img
                  className="w-10 h-10 rounded-full"
                  src={data?.authorImage || ""}
                  alt=""
                />
                <div className="font-medium ">
                  <div className="text-base font-semibold text-dark-250">
                    {data?.authorName}
                  </div>
                </div>
              </div>
              <div className=" text-[#878787] text-xs font-medium">
                {new Date(data?.createdAt)?.toLocaleDateString("en-GB", {
                  day: "2-digit",
                  month: "long",
                  year: "numeric",
                })}
              </div>
            </div>
            <h1 className="text-lg font-semibold text-[#3C3C3C] mt-6">
              {data?.title}
            </h1>
            <div className="mt-7 text-[#464646] text-base font-normal leading-6">
              <div
                dangerouslySetInnerHTML={{ __html: data?.desctiption || "" }}
              />
            </div>
          </div>
        </div>

        {/* <div className='flex justify-center items-center mt-24'>
                    <div className="flex items-center gap-3">
                        <button type="button" className=" text-red-50 border-red-50 border hover:bg-red-50 text-sm font-medium px-5 py-2.5 text-center inline-flex items-center font-inter">
                            PREV BLOG
                        </button>
                        <button type="button" className="text-white bg-red-50 hover:bg-red-50 text-sm font-medium px-5 py-2.5 text-center inline-flex items-center font-inter">
                            NEXT BLOG
                            <svg className="w-3.5 h-3.5 ml-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M1 5h12m0 0L9 1m4 4L9 9" />
                            </svg>
                        </button>
                    </div>
                </div> */}
      </div>
    </main>
  );
}

export default DetailsPage;
