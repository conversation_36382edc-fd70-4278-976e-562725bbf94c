"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import SearchEmpty from "@/components/presentation/searchResult";
import Link from "next/link";
import ProductCard from "@/components/main/productCard";

function Page() {
  const searchParams = useSearchParams();
  const search = searchParams.get("key");

  const [data, setData] = useState<any>();

  useEffect(() => {
    async function getData() {
      try {
        const res = await fetch("/backend/search", {
          method: "post",
          cache: "no-cache",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ key: search }),
        });
        const result = await res.json();
        setData(result?.data);
        if (!res.ok || !result?.status) {
          throw new Error("Failed to fetch data");
        }
      } catch (error) {
        console.log(error);
      }
    }
    getData();
  }, [search]);

  return (
    <main className="w-full bg-[#FCFCFC]">
      <div className="container py-10 mb-10">
        <h4 className="text-dark-50 text-2xl font-semibold">Search Result</h4>
        {!!data?.data?.length ? (
          <div className="grid grid-cols-2 gap-x-20 gap-y-7 mt-7">
            {data?.data?.map((item: any, index: number) => (
              <ProductCard key={index} data={item} isSearch={true} />
            ))}
          </div>
        ) : (
          <SearchEmpty />
        )}
      </div>
    </main>
  );
}

export default Page;
