"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import TimeLine from "@/components/main/timeline";
import { Icons } from "@/components/presentation/icons";
import { useFetch } from "@/utils/hooks/useFeatch";
import { Order } from "@/utils/types/common";
import apiRoutes from "@/utils/api/routes";
import CONSTANT from "@/utils/constant";
import Spinner from "@/components/presentation/spinner";
import { downloadInvoice } from "@/utils/downloadHelper";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import submitData from "@/utils/api/submitData";

function Page({ params }: { params: { id: string } }) {
  const [isShow, setisShow] = useState(false);
  const [isCancelling, setisCancelling] = useState(false);
  const [isReload, setisReload] = useState(false);
  const [token, setToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    const accessToken = localStorage.getItem("accessToken") ?? undefined;
    setToken(accessToken);
  }, []);

  const { data, isLoading } = useFetch<Order>({
    url: apiRoutes.client.order,
    options: { body: JSON.stringify({ orderId: params?.id }) },
    token: token || undefined,
    start: !!token,
    dep: isReload,
  });

  const cancelOrder = async () => {
    try {
      setisShow(false);
      setisCancelling(true);
      const response = await submitData({
        url: apiRoutes.client.cancelOrder,
        body: { orderId: params?.id },
        token: token,
      });
      if (response?.status) {
        setisReload(!isReload);
      }
    } catch (error) {
    } finally {
      setisCancelling(false);
    }
  };
  return (
    <div className="container min-h-[60vh] my-10">
      {!isLoading ? (
        <div>
          <h1 className="font-semibold text-dark-300 text-4xl mb-7">
            Order details
          </h1>
          <div className="border p-7 rounded-lg">
            <div className="bg-white border-b-black rounded-xl mb-7">
              <div className="flex justify-between items-center gap-5 w-full">
                <div className="w-6/12">
                  <div className="flex justify-start gap-5">
                    <div>
                      <Image
                        className="rounded-lg aspect-squar"
                        width={140}
                        height={135}
                        alt="product"
                        src={data?.items[0]?.image || ""}
                      />
                    </div>
                    <div className="text-start">
                      <h4 className=" text-sm font-medium text-dark-500">
                        {data?.items[0]?.title}
                      </h4>
                      <div>
                        <span className="bg-green-700 py-0.5 rounded px-3 text-white text-xs font-normal">
                          {data?.items[0]?.product?.rating} ★
                        </span>
                      </div>
                      <span className="text-[#8C8C8C] text-[0.85rem] font-normal">
                        Qty : 1 Kg
                      </span>
                    </div>
                  </div>
                </div>

                <div className="w-2/12 text-center">
                  <h4 className="text-base text-dark-500 font-semibold">
                    {" "}
                    ₹ {data?.totalAmount}
                  </h4>
                  {/* <span className="text-[0.7rem] font-normal text-dark-450">3 offer applied</span> */}
                </div>

                {data?.orderStatus && (
                  <div className="w-4/12 flex flex-col justify-start items-center gap-4">
                    <p className="text-[0.7rem] font-normal text-dark-500 flex gap-2">
                      {data?.orderStatus == CONSTANT.ORDER_STATUS.DELIVERED ? (
                        <Icons.greenCheck />
                      ) : (
                        <Icons.processing />
                      )}
                      {CONSTANT.ORDER_STATUS_INFO[data?.orderStatus || 0]}
                      {data?.statusHistory?.delivered_on && (
                        <span>
                          Delivered ON{" "}
                          {new Date(
                            data?.statusHistory?.delivered_on,
                          )?.toLocaleDateString()}
                        </span>
                      )}
                    </p>
                    {data?.orderStatus == CONSTANT.ORDER_STATUS.DELIVERED && (
                      <p className="text-[10px] font-light text-dark-450">
                        Your item has been delivered
                      </p>
                    )}
                    {data?.orderStatus < 4 && (
                      <button
                        disabled={isCancelling}
                        className="text-[#E03B3B] text-xs font-medium bg-[#FFE0E0] py-2 px-4 rounded-lg"
                        onClick={() => setisShow(true)}
                      >
                        Cancel order {isCancelling && <Icons.loading />}
                      </button>
                    )}
                    {data?.orderStatus == CONSTANT.ORDER_STATUS.DELIVERED && (
                      <button className="text-[#4280BC] text-xs font-normal flex items-center gap-3">
                        <Icons.blueStar /> Rate & Review Product{" "}
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div></div>

            <hr className="w-full h-[0.7px] bg-dark-450" />
            <div className="mt-3">
              <h6 className="text-sm font-medium text-dark-500">
                Order Items - {data?.items?.length}
              </h6>
              <div className="grid grid-cols-3 mt-3 divide-x gap-3">
                {data?.items?.map((newitem: any, index) => (
                  <div key={index}>
                    <div className="flex justify-start gap-5 col-span-5">
                      <div>
                        <Image
                          className="rounded-lg aspect-squar"
                          width={140}
                          height={135}
                          alt="product"
                          src={newitem?.image || "/images/product/product.png"}
                        />
                      </div>
                      <div className="text-start">
                        <h4 className=" text-xs font-medium text-dark-500">
                          {newitem?.title}
                        </h4>
                        <div>
                          <span className="bg-green-700  rounded px-2.5 text-white  text-[0.6rem] font-extralight">
                            {newitem?.product?.rating} ★
                          </span>
                        </div>
                        <span className="text-[#8C8C8C] text-[0.65rem] font-normal">
                          Qty : {newitem?.quantity} {newitem?.product?.unit}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <hr className="w-full h-[0.7px] bg-dark-450 my-6" />
            <div className="mt-5">
              <div className="grid grid-cols-12 gap-5 justify-between">
                <div className="col-span-4">
                  <h5 className="text-sm font-medium text-dark-500">
                    Delivery Address
                  </h5>
                  <h5 className="text-sm font-medium text-dark-500 mt-3">
                    {data?.shippingAddress?.name}
                  </h5>
                  <p className="font-normal text-[0.7rem] text-dark-500">
                    {data?.shippingAddress?.address}
                  </p>
                  <p className="font-normal text-[0.7rem] text-dark-500">
                    {data?.shippingAddress?.landmark}{" "}
                    {data?.shippingAddress?.city}{" "}
                    {data?.shippingAddress?.pincode}{" "}
                  </p>

                  <h5 className="text-sm font-medium text-dark-500 mt-3">
                    Phone Number
                  </h5>
                  <p className="font-normal text-xs text-dark-500">
                    {data?.shippingAddress?.phone}
                  </p>
                </div>
                <div className="col-span-8 ">
                  {data?.orderStatus && (
                    <TimeLine
                      orderStatus={data?.orderStatus}
                      statusHistory={data?.statusHistory || []}
                    />
                  )}
                  <hr className="w-full h-[0.7px] bg-dark-450 my-3" />
                  <div className="grid grid-cols-2 mt-3">
                    <div>
                      <h4 className="text-sm font-medium text-dark-500 mb-5">
                        Your Rewards
                      </h4>
                      {/* <div className="flex gap-4 mt-3">
                                                        <Icons.superIcon />
                                                        <p className="font-normal text-[0.7rem] text-dark-500">12 SuperCoins Cashback</p>
                                                    </div> */}

                      <div className="flex gap-4 mt-3 ">
                        <Icons.delivery />
                        <p className="font-normal text-[0.7rem] text-dark-500">
                          Free Delivery
                        </p>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-dark-500 mb-5">
                        Other Actionsss
                      </h4>
                      {data?.invoice?.url && (
                        <button
                          onClick={() =>
                            downloadInvoice(
                              data.invoice.url,
                              data.id || data._id,
                            )
                          }
                          className="text-[#195DAF] w-fit font-medium text-[0.65rem] py-2 px-5 bg-[#D2E9FF] rounded-sm flex gap-2 items-center hover:bg-[#C1E0FF] transition-colors"
                        >
                          <Icons.invoice /> Download Invoice
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div>
          <Spinner />{" "}
        </div>
      )}

      <AlertDialog open={isShow} onOpenChange={() => setisShow(false)}>
        <AlertDialogContent>
          <AlertDialogHeader className="pb-5">
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will cancel your order.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={cancelOrder} className="bg-red-50">
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

export default Page;
