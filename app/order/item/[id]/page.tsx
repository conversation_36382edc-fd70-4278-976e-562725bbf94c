"use client";

import React, { useState } from "react";
import Image from "next/image";
import TimeLine from "@/components/main/timeline";
import { Icons } from "@/components/presentation/icons";
import { useFetch } from "@/utils/hooks/useFeatch";
import { Order } from "@/utils/types/common";
import apiRoutes from "@/utils/api/routes";
import { useSession } from "next-auth/react";
import CONSTANT from "@/utils/constant";
import Spinner from "@/components/presentation/spinner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import submitData from "@/utils/api/submitData";
import { useRouter } from "next/navigation";

function Page({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isShow, setisShow] = useState(false);
  const [isCancelling, setisCancelling] = useState(false);
  const [isReload, setisReload] = useState(false);

  const { data, isLoading } = useFetch<Order>({
    url: apiRoutes.client.order,
    options: { body: JSON.stringify({ orderId: params?.id }) },
    token: session?.token || "",
    start: session?.token,
    dep: isReload,
  });

  const cancelOrder = async () => {
    try {
      setisShow(false);
      setisCancelling(true);
      const response = await submitData({
        url: apiRoutes.client.cancelOrder,
        body: { orderId: params?.id },
        token: session?.token,
      });
      if (response?.status) {
        setisReload(!isReload);
      }
    } catch (error) {
    } finally {
      setisCancelling(false);
    }
  };
  return (
    <div className="container min-h-[60vh] my-10">
      <div>
        <h1 className="font-semibold text-dark-300 text-4xl mb-7">
          Order details
        </h1>
        <div className="border p-7 rounded-lg">
          <div className="mt-3">
            <h5 className="text-sm font-medium text-dark-500">
              Delivery Address
            </h5>
            <div className="grid grid-cols-3 mt-3 divide-x gap-3">
              <div className="flex justify-start gap-5 col-span-5">
                <div>
                  <Image
                    className="rounded-lg aspect-squar"
                    width={140}
                    height={135}
                    alt="product"
                    src={"/images/product/product.png"}
                  />
                </div>
                <div className="text-start">
                  <h4 className=" text-xs font-medium text-dark-500">ghjg</h4>
                  <div>
                    <span className="bg-green-700  rounded px-2.5 text-white  text-[0.6rem] font-extralight">
                      465 ★
                    </span>
                  </div>
                  <span className="text-[#8C8C8C] text-[0.65rem] font-normal">
                    Qty : t
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <hr className="w-full h-[0.7px] bg-dark-450 my-6" />
        <div className="mt-5">
          <div className="grid grid-cols-12 gap-5 justify-between">
            <div className="col-span-4">
              <h5 className="text-sm font-medium text-dark-500">
                Delivery Address
              </h5>
              <h5 className="text-sm font-medium text-dark-500 mt-3">
                {data?.shippingAddress?.name}
              </h5>
              <p className="font-normal text-[0.7rem] text-dark-500">
                {data?.shippingAddress?.address}
              </p>
              <p className="font-normal text-[0.7rem] text-dark-500">
                {data?.shippingAddress?.landmark} {data?.shippingAddress?.city}{" "}
                {data?.shippingAddress?.pincode}{" "}
              </p>

              <h5 className="text-sm font-medium text-dark-500 mt-3">
                Phone Number
              </h5>
              <p className="font-normal text-xs text-dark-500">
                {data?.shippingAddress?.phone}
              </p>
            </div>
            <div className="col-span-8 ">
              {data?.orderStatus && (
                <TimeLine
                  orderStatus={data?.orderStatus}
                  statusHistory={data?.statusHistory || []}
                />
              )}
              <hr className="w-full h-[0.7px] bg-dark-450 my-3" />
              <div className="grid grid-cols-2 mt-3">
                <div>
                  <h4 className="text-sm font-medium text-dark-500 mb-5">
                    Your Rewards
                  </h4>

                  <div className="flex gap-4 mt-3 ">
                    <Icons.delivery />
                    <p className="font-normal text-[0.7rem] text-dark-500">
                      Free Delivery
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-dark-500 mb-5">
                    Other Actions
                  </h4>
                  {data?.invoice?.url && (
                    <a
                      download={true}
                      href={`${data?.invoice?.url}`}
                      className="text-[#195DAF] w-fit font-medium text-[0.65rem] py-2 px-5 bg-[#D2E9FF] rounded-sm flex gap-2"
                    >
                      {" "}
                      <Icons.invoice /> Download Invoice
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <AlertDialog open={isShow} onOpenChange={() => setisShow(false)}>
        <AlertDialogContent>
          <AlertDialogHeader className="pb-5">
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will cancel your order.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={cancelOrder} className="bg-red-50">
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

export default Page;
