"use client";

import React, { useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import Otp from "@/components/page/auth/otp";
import MobileLogin from "@/components/page/auth/mobile";
import Link from "next/link";
import { useRouter } from "next/navigation";

function Auth() {
  const [isOpen, setisOpen] = useState(true);
  const [phone, setphone] = useState("");
  const router = useRouter();

  const handleOtpSent = ({ phone }: { phone: string }): any => {
    setphone(phone);
  };

  return (
    <div className="h-screen flex justify-center items-center">
      <div>
        <Link href="/" className="bg-red-500 text-white py-2 px-4 rounded-md">
          Back to Home{" "}
        </Link>
      </div>

      <Dialog open={isOpen} onOpenChange={() => router.push("/")} defaultOpen>
        <DialogContent className="sm:max-w-[50vw]  p-0">
          <div className="w-full grid grid-cols-2 h-min-[50vh]">
            <div className=" bg-[url('/images/bg/login.png')] bg-no-repeat bg-cover bg-center"></div>

            <div className="p-10 h-auto">
              <h2 className="text-[2rem]  font-semibold text-dark-50">
                Log in
              </h2>
              <h3 className="mt-5 text-[1.4rem] font-normal text-dark-500">
                Hello !
              </h3>
              <h2 className="text-2xl font-medium text-dark-50">
                Welcome back
              </h2>

              <div>
                {phone ? (
                  <Otp phone={phone} close={() => setisOpen(false)} />
                ) : (
                  <MobileLogin handleOtp={handleOtpSent} />
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default Auth;
