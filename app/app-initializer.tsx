"use client";

import { useEffect } from "react";
import { useCartStore } from "@/utils/store/cart";
import { useWishlistStore } from "@/utils/store/wishlist";
import apiRoutes from "@/utils/api/routes";

const AppInitializer = () => {
  const setCartList = useCartStore((state) => state.setCartList);
  const setWishList = useWishlistStore((state) => state.setWishList);

  useEffect(() => {
    const token = localStorage.getItem("accessToken");
    if (!token) return;

    const fetchCart = async () => {
      try {
        const res = await fetch(apiRoutes.client.cartItems, {
          method: "post",
          cache: "no-cache",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const result = await res.json();
        if (result?.status) {
          setCartList(result.data);
        }
      } catch (err) {
        console.error("Failed to load cart", err);
      }
    };

    const fetchWishlist = async () => {
      try {
        const res = await fetch(apiRoutes.client.wishListItems, {
          method: "post",
          cache: "no-cache",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const result = await res.json();
        if (result?.status) {
          setWishList(result?.data);
        }
      } catch (err) {
        console.error("Failed to load wishlist", err);
      }
    };

    fetchCart();
    fetchWishlist();
  }, []);

  return null;
};

export default AppInitializer;
