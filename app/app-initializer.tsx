"use client";

import { useEffect } from "react";
import { useCartStore } from "@/utils/store/cart";
import { useWishlistStore } from "@/utils/store/wishlist";
import apiRoutes from "@/utils/api/routes";
import {
  getCurrentToken,
  setupAuthStateListener,
} from "@/utils/auth/authEvents";

const AppInitializer = () => {
  const setCartList = useCartStore((state) => state.setCartList);
  const setWishList = useWishlistStore((state) => state.setWishList);

  const fetchUserData = async () => {
    const token = getCurrentToken();
    if (!token) return;

    const fetchCart = async () => {
      try {
        const res = await fetch(apiRoutes.client.cartItems, {
          method: "post",
          cache: "no-cache",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const result = await res.json();
        if (result?.status) {
          setCartList(result.data);
        }
      } catch (err) {
        console.error("Failed to load cart", err);
      }
    };

    const fetchWishlist = async () => {
      try {
        const res = await fetch(apiRoutes.client.wishListItems, {
          method: "post",
          cache: "no-cache",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const result = await res.json();
        if (result?.status) {
          setWishList(result?.data);
        }
      } catch (err) {
        console.error("Failed to load wishlist", err);
      }
    };

    fetchCart();
    fetchWishlist();
  };

  useEffect(() => {
    // Initial data fetch
    fetchUserData();

    // Set up auth state listener
    const cleanup = setupAuthStateListener(fetchUserData);

    return cleanup;
  }, []);

  return null;
};

export default AppInitializer;
