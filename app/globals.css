@tailwind base;
@tailwind components;
@tailwind utilities;

.glass-card:hover {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 20px;
}

.slick-track {
  margin-left: 0 !important;
}

.slick-list {
  padding: 0 20% 0 0 !important;
}

.caros .slick-next:before {
  content: none !important;
}

.caros .slick-prev:before {
  content: none !important;
}

.caros .slick-next {
  margin-left: 40px;
  width: 32px !important;
  height: 32px !important;
  background-image: url(/images/icon/next.png) !important;
  background-repeat: no-repeat !important;
  background-color: #28ace2 !important;
  /* border-radius: 100%; */
  background-position: center !important;
  background-size: 47px !important;
}

.caros .slick-prev {
  width: 32px !important;
  height: 32px !important;
  background-image: url(/images/icon/prev.png) !important;
  background-repeat: no-repeat !important;
  background-color: #28ace2 !important;
  /* border-radius: 100%; */
  background-position: center !important;
  background-size: 47px !important;
}
