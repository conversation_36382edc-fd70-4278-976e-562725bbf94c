import "./globals.css";
import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import { Toaster } from "@/components/ui/toaster";
import Providers from "@/utils/auth/provider";
import AppInitializer from "./app-initializer";
import { ToastContainer } from "react-toastify";

const inter = Inter({ subsets: ["latin"] });
const poppins = Poppins({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Ficean",
  description: "Ficean",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} ${poppins.className} `}>
        <Providers>
          <AppInitializer />
          <Header />
          <ToastContainer
            position="top-center"
            toastClassName="!w-auto !max-w-none !min-w-0 px-4 py-3 bg-white shadow-md rounded-lg"
          />
          {children}
          <Footer />
        </Providers>
        <Toaster />
      </body>
    </html>
  );
}
