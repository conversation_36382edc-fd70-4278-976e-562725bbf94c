/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    return [
      {
        source: "/backend/:path*",
        destination: `${process.env.BACKEND_URL}/api/:path*`, // Proxy to Backend
      },
      {
        source: "/media/:path",
        destination: `${process.env.BACKEND_URL}/media/:path*`,
      },
      {
        source: "/invoice/:path",
        destination: `${process.env.BACKEND_URL}/invoice/:path*`,
      },
      {
        source: "/payment/:path",
        destination: `${process.env.BACKEND_URL}/payment/:path*`,
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "ficean-bucket.s3.ap-south-1.amazonaws.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
};

module.exports = nextConfig;
